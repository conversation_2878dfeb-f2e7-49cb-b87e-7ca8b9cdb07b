# Achievement System Integration Guide

## ✅ **Fully Implemented & Automatic**

The following achievements are now **fully implemented** and will track automatically:

### **Bronze Achievements**
- ✅ `primeira_picareta` - Craft first pickaxe (CraftItemEvent)
- ✅ `batismo_de_pedra` - Mine 500 blocks (BlockBreakEvent)
- ✅ `minerador_junior` - Reach rank Aprendiz (Rankup command)
- ✅ `maos_sujas` - Collect 100 coal blocks (BlockBreakEvent)
- ✅ `dia_de_pagamento` - Sell first blocks (PlacasListener integration)
- ✅ `pe_de_meia` - Reach 1,000,000 money (Economy integration)
- ✅ `inicio_de_jornada` - Play 1 hour total (PlaytimeManager)
- ✅ `recruta_de_clan` - Join a clan (ClanCommand integration)

### **Silver Achievements**
- ✅ `minerador_consistente` - Mine 25,000 blocks (BlockBreakEvent)
- ✅ `subterraneo_serio` - Reach rank Sub-Chefe (Rankup command)
- ✅ `carteira_pesada` - Reach 1,000,000,000 money (Economy integration)
- ✅ `pvp_casual` - Win first PvP combat (PlayerDeathEvent)
- ✅ `negociante_de_rua` - Sell 10,000+ blocks (PlacasListener integration)
- ✅ `capataz_da_mina` - Play 12 hours total (PlaytimeManager)

### **Gold Achievements**
- ✅ `mao_de_diamante` - Collect 1,000 diamonds (BlockBreakEvent)
- ✅ `rei_da_mina` - Reach last rank without prestige (Rankup command)
- ✅ `pvp_dominante` - Win 50 PvP combats (PlayerDeathEvent)
- ✅ `milionario_a_vista` - Reach 450,000,000,000 money (Economy integration)
- ✅ `veterano_ativo` - Play 50 hours total (PlaytimeManager)
- ✅ `comerciante_solido` - Make 250 sales in /shop (PlacasListener integration)
- ✅ `minador_imparavel` - Mine 250,000 blocks (BlockBreakEvent)
- ✅ `negocio_fechado` - Sell 1,000,000+ worth in /loja (PlacasListener integration)

### **Platinum Achievements**
- ✅ `lenda_da_mina` - Reach Prestige 1 (PrestigeCommand integration)
- ✅ `milionario_oficial` - Reach 1,000,000,000,000 money (Economy integration)
- ✅ `pvp_sanguinario` - Kill 150 players in PvP (PlayerDeathEvent)
- ✅ `tempo_e_ouro` - Play 150 hours total (PlaytimeManager)
- ✅ `a_maquina` - Mine 1,000,000 blocks (BlockBreakEvent)
- ✅ `comerciante_lendario` - Sell 10,000,000+ total (PlacasListener integration)

### **Mythic Achievements**
- ✅ `deus_da_mina` - Reach Prestige 10 (PrestigeCommand integration)
- ✅ `sombra_do_pvp` - Kill 500 players in PvP (PlayerDeathEvent)
- ✅ `tempo_e_poder` - Play 500 hours total (PlaytimeManager)

## ⚠️ **Requires Manual Integration**

These achievements need you to add integration calls to your existing systems:

### **Mine Visit Tracking**
```java
// Add this when player enters a mine area:
AchievementSpecialManager.getInstance().onMineVisit(player, mineName);
```
**Affects:** `explorador_local`, `pisaste_cada_pedra`

### **Tool Usage Tracking**
```java
// When player uses anvil:
AchievementSpecialManager.getInstance().onAnvilUse(player);

// When tool breaks (add to item durability handling):
AchievementSpecialManager.getInstance().onToolBreak(player, brokenTool);

// When using Efficiency V pickaxe:
AchievementSpecialManager.getInstance().onEfficiencyVUse(player);
```
**Affects:** `engenharia_manual`, `ferramenta_partida`, `forca_bruta`, `senhor_da_picareta`

### **Plot System Integration**
```java
// When player creates plot:
AchievementSpecialManager.getInstance().onPlotCreate(player);

// When player gives plot permission:
AchievementSpecialManager.getInstance().onPlotPermitGiven(player);
```
**Affects:** `construtor_iniciado`, `tatica_de_grupo`

### **Event System Integration**
```java
// When player participates in official event:
AchievementSpecialManager.getInstance().onEventParticipation(player);
```
**Affects:** `cacador_de_eventos`

### **Leaderboard Integration**
```java
// Daily check if player is in top 10 (add to your leaderboard update task):
AchievementSpecialManager.getInstance().onLeaderboardCheck(player, isInTop10);
```
**Affects:** `top_10_mineracao`, `top_dos_tops`, `nome_gravado_em_pedra`

### **PvP Death Tracking**
```java
// When player dies in PvP zones (add to your PvP death handler):
AchievementSpecialManager.getInstance().onPvPDeath(player);

// Daily task to check death-free streaks:
AchievementSpecialManager.getInstance().checkPvPDeathFreeStreak(player);
```
**Affects:** `inquebravel`

## 🔧 **Special Cases**

### **Weekly PvP Events**
```java
// For conquistador_de_sangue - track weekly PvP event wins
// You'll need to implement this based on your event system
AchievementHelper.incrementProgress(player, "conquistador_de_sangue");
```

### **Complete All Achievements**
```java
// For lenda_absoluta - check when player completes all other achievements
// This should be triggered when any achievement is completed
// Check if player now has 100% completion
```

## 🚀 **Ready to Use**

The system is now fully integrated and ready! Here's what works automatically:

1. **Player joins** → Achievement data loaded from Velocity
2. **Player mines blocks** → Mining achievements tracked
3. **Player kills in PvP** → PvP achievements tracked  
4. **Player sells items** → Shop achievements tracked
5. **Player ranks up** → Rank achievements tracked
6. **Player gains money** → Money achievements tracked
7. **Player joins clan** → Clan achievements tracked
8. **Time passes** → Playtime achievements tracked
9. **Achievement completed** → Rewards executed, Velocity notified

## 📋 **Commands Available**

- `/achievements` - View all achievements with progress
- `/ach` - Short alias
- `/conquistas` - Portuguese alias

## 🔄 **Next Steps**

1. **Test the automatic systems** - Most achievements should work immediately
2. **Add manual integrations** - Use the code snippets above for remaining achievements
3. **Fine-tune thresholds** - Adjust achievement requirements if needed
4. **Test with Velocity** - Ensure plugin messaging works correctly

The core system is complete and most achievements will work out of the box!
