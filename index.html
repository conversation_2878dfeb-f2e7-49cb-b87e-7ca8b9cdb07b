<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CraftAndHelps</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-family: sans-serif;
            background: linear-gradient(-45deg, #0d0d0d, #1a1a1a, #0d0d0d, #2b2b2b);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .container {
            /* No specific styles needed for centering with flexbox on the body */
        }

        h1 {
            font-size: 5rem; /* Large text size */
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        p {
            font-size: 1.5rem; /* Smaller text size */
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CraftAndHelps</h1>
        <p>Brevemente</p>
    </div>
</body>
</html>
