# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- main

pool:
  vmImage: ubuntu-latest

steps:
# Send Discord Info Webhook
- script: |
    webhook_url="https://discord.com/api/webhooks/1233390472462995518/Y6KikLcO5WWfJf0dRS5eYD8Cl7QuX3ralBB5qpEUSnwwl0cuJo6BtTaFMsO7BkuS0XCV"
    embed='{
      "title": "Deployment Notification",
      "description": "Deployment of the new update has been done successfully.",
      "color": 3066993,
      "fields": [
        {
          "name": "Commit ID",
          "value": "'${BUILD_SOURCEVERSION}'",
          "inline": true
        },
        {
          "name": "Commit Message",
          "value": "'${BUILD_SOURCEVERSIONMESSAGE}'",
          "inline": true
        }
      ]
    }'
    body='{
      "username": "DevOps & GitHub",
      "avatar_url": "http://craftandhelps.com/logo.png",
      "embeds": ['"$embed"']
    }'
    curl -H "Content-Type: application/json" -X POST -d "$body" $webhook_url
  displayName: 'Send Discord Info Webhook'
  retryCountOnTaskFailure: 3  # Number of retries on task failure
  condition: succeeded()

# Send Failure Discord Embed Webhook
- script: |
    webhook_url="https://discord.com/api/webhooks/1233390472462995518/Y6KikLcO5WWfJf0dRS5eYD8Cl7QuX3ralBB5qpEUSnwwl0cuJo6BtTaFMsO7BkuS0XCV"
    embed='{
      "title": "Deployment Failure Notification",
      "description": "Alert: Deployment has failed. Check the logs for more details.",
      "color": 15158332
    }'
    body='{
      "username": "DevOps & GitHub",
      "avatar_url": "http://craftandhelps.com/logo.png",
      "embeds": ['"$embed"']
    }'
    curl -H "Content-Type: application/json" -X POST -d "$body" $webhook_url
  displayName: 'Send Failure Discord Embed Webhook'
  condition: failed()