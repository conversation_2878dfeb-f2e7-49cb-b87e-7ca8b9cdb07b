name: Deployment Workflow

# Trigger the workflow on pushes to the main branch
on:
  push:
    branches:
      - main

jobs:
  notify-success:
    name: Notify Success
    runs-on: ubuntu-latest
    if: success()
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Send Discord Info Webhook
        env:
          GITHUB_SHA: ${{ github.sha }}
          GITHUB_EVENT_HEAD_COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
        run: |
          webhook_url="https://discord.com/api/webhooks/1233390472462995518/Y6KikLcO5WWfJf0dRS5eYD8Cl7QuX3ralBB5qpEUSnwwl0cuJo6BtTaFMsO7BkuS0XCV"
          commit_id="${GITHUB_SHA}" # GitHub Actions provides the commit ID in GITHUB_SHA
          # Fallback: Use git to get the commit message if GITHUB_EVENT_HEAD_COMMIT_MESSAGE is empty
          commit_message="${GITHUB_EVENT_HEAD_COMMIT_MESSAGE}"
          if [ -z "$commit_message" ]; then
            commit_message=$(git log -1 --pretty=%B)
          fi
          
          embed='{
            "title": "Deployment Alerta - Rankup",
            "description": "Código foi atualizado com sucesso.",
            "color": 3066993,
            "fields": [
              {
                "name": "Commit ID",
                "value": "'"${commit_id}"'",
                "inline": true
              },
              {
                "name": "Commit Message",
                "value": "'"${commit_message}"'",
                "inline": true
              }
            ]
          }'
          body='{
            "username": "GitHub Action",
            "avatar_url": "http://***************/your-logo.png",
            "embeds": ['"$embed"']
          }'
          curl -H "Content-Type: application/json" -X POST -d "$body" "$webhook_url"


  notify-failure:
    name: Notify Failure
    runs-on: ubuntu-latest
    if: failure() # Run this job only if the workflow fails
    steps:
      - name: Send Failure Discord Embed Webhook
        run: |
          webhook_url="https://discord.com/api/webhooks/1233390472462995518/Y6KikLcO5WWfJf0dRS5eYD8Cl7QuX3ralBB5qpEUSnwwl0cuJo6BtTaFMsO7BkuS0XCV"
          embed='{
            "title": "Deployment Falhado!",
            "description": "Alert: Deployment has failed. Check the logs for more details.",
            "color": 15158332
          }'
          body='{
            "username": "DevOps & GitHub",
            "avatar_url": "http://***************/your-logo.png",
            "embeds": ['"$embed"']
          }'
          curl -H "Content-Type: application/json" -X POST -d "$body" "$webhook_url"
