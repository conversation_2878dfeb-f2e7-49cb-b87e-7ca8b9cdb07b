package me.miguel19877.dev.listeners;

import me.miguel19877.dev.gui.AchievementCategoryGUI;
import me.miguel19877.dev.gui.AchievementGUI;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

/**
 * <PERSON><PERSON> clicks in achievement GUIs
 */
public class AchievementGUIListener implements Listener {
    
    private final AchievementGUI mainGUI = new AchievementGUI();
    private final AchievementCategoryGUI categoryGUI = new AchievementCategoryGUI();
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack clickedItem = event.getCurrentItem();
        
        // Handle main achievement GUI clicks
        if (AchievementGUI.isMainAchievementGUI(event.getInventory())) {
            event.setCancelled(true);
            
            if (clickedItem == null || !clickedItem.hasItemMeta()) {
                return;
            }
            
            String category = AchievementGUI.getCategoryFromSlot(event.getSlot());
            if (category != null) {
                // Open category GUI
                categoryGUI.openCategoryGUI(player, category);
            }
            return;
        }
        
        // Handle category GUI clicks
        if (AchievementCategoryGUI.isCategoryGUI(event.getInventory())) {
            event.setCancelled(true);
            
            if (clickedItem == null) {
                return;
            }
            
            // Check if back button was clicked
            if (AchievementCategoryGUI.isBackButton(clickedItem, event.getSlot())) {
                // Go back to main GUI
                mainGUI.openMainGUI(player);
            }
            
            // Individual achievement items don't need special handling
            // They're just for display
            return;
        }
    }
}
