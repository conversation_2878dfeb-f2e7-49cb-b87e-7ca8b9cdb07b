package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.ClanManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.PrestigeManager;
import me.miguel19877.dev.permissions.Permissions;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.UUID;

public class ChatListener implements Listener, CommandExecutor {

    private static HashMap<UUID, Integer> chatMode = new HashMap<>();

    public ChatListener() {
        this.chatMode = new HashMap<>();
    }

    public static void setChatMode(UUID uuid, String mode) {
        if (chatMode.containsKey(uuid)) {
            chatMode.remove(uuid);
        }
        int modeInt = 0;
        String modo = mode;
        switch (mode.toLowerCase()) {
            case "global":
                modeInt = 0;
                break;
            case "local":
                modeInt = 1;
                break;
            case "staff":
                if (Permissions.getGrupoId(Bukkit.getPlayer(uuid)) >= 10) {
                    modeInt = 2;
                }else {
                    LanguageManager.getInstance().sendMessage(Bukkit.getPlayer(uuid), "chat.mode_not_exists_global");
                    modo = "global";
                    modeInt = 0;
                }
                break;
            case "clan":
                if (ClanManager.isPlayerInClan(uuid)) {
                    modeInt = 3;
                    modo = "clan";
                }else {
                    LanguageManager.getInstance().sendMessage(Bukkit.getPlayer(uuid), "chat.not_in_clan_global");
                    modo = "global";
                    modeInt = 0;
                }
                break;
            default:
                LanguageManager.getInstance().sendMessage(Bukkit.getPlayer(uuid), "chat.mode_not_exists_global");
                modo = "global";
                modeInt = 0;
                break;
        }
        LanguageManager.getInstance().sendMessage(Bukkit.getPlayer(uuid), "chat.mode_changed", modo);
        chatMode.put(uuid, modeInt);
    }

    public static int getChatMode(UUID uuid) {
        if(chatMode.containsKey(uuid)) {
            return chatMode.get(uuid);
        }
        return 0;
    }

    public static void removeChatMode(UUID uuid) {
        if(chatMode.containsKey(uuid)) {
            chatMode.remove(uuid);
        }
    }

    public static String convert(long value) {
        if (value < 1000) {
            return String.valueOf(value);
        }
        int exp = (int) (Math.log(value) / Math.log(1000));
        return String.format("%.1f%c", value / Math.pow(1000, exp), "kMBTQ".charAt(exp-1));
    }


    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("chat")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                if (Permissions.getGrupoId(p) >= 10) {
                    if (args.length == 0) {
                        sender.sendMessage("§c/chat <global|local|staff|clan> - Alterar o modo do chat.");
                    }else if (args.length == 1) {
                        setChatMode(p.getUniqueId(), args[0]);
                    }else {
                        sender.sendMessage("§c/chat <global|local|staff|clan> - Alterar o modo do chat.");
                    }
                }else {
                    if (args.length == 0) {
                        LanguageManager.getInstance().sendMessage(p, "chat.usage");
                    }else if (args.length == 1) {
                        setChatMode(p.getUniqueId(), args[0]);
                    }else {
                        LanguageManager.getInstance().sendMessage(p, "chat.usage");
                    }
                }
            }else {
                sender.sendMessage("§cYou need to be a player to execute this command."); // Console fallback
            }
        }
        return true;
    }


    @EventHandler
    public void chat(AsyncPlayerChatEvent e) {
        Player p = e.getPlayer();
        int prestige = PrestigeManager.getPrestigeLevel(p);
        String grupo = Permissions.getGrupo(p);
        int rankid = RankSystem.getRankId(p);
        String rank = RankSystem.getRankName(rankid);
        StringBuilder format = new StringBuilder();
        switch (getChatMode(p.getUniqueId())) {
            case 0:
                format.append("§7[§fG§7] ");
                break;
            case 1:
                format.append("§7[§fL§7] ");
                break;
            case 2:
                format.append("§7[§fS§7] ");
                break;
            case 3:
                format.append("§7[§fC§7] ");
                break;
            default:
                break;
        }
        format.append(grupo + " §f");
        if (prestige > 0) {
            format.append("§6[P-" + prestige + "] §r");
        }
        format.append(rank + " ");
        format.append(p.getName() + "§7: §f" + e.getMessage());
        long money2 = Economy.getMoney(p.getUniqueId());
        String money = convert(money2);
        String finalString = ChatColor.translateAlternateColorCodes('&', format.toString());
        e.setCancelled(true);
        TextComponent message = generateMessage(p, grupo, rank, money, finalString, prestige);
        if (getChatMode(p.getUniqueId()) == 0) {
            Rankup.getInstance().getServer().spigot().broadcast(message);
        }else if (getChatMode(p.getUniqueId()) == 1) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getLocation().distance(p.getLocation()) < 20) {
                    player.spigot().sendMessage(message);
                }
            }
        }else if (getChatMode(p.getUniqueId()) == 2) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (Permissions.getGrupoId(player) >= 10) {
                    player.spigot().sendMessage(message);
                }
            }
        }else if (getChatMode(p.getUniqueId()) == 3) {
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (ClanManager.isPlayerInClan(player.getUniqueId()) && ClanManager.getClanName(player.getUniqueId()).equals(ClanManager.getClanName(p.getUniqueId()))) {
                    player.spigot().sendMessage(message);
                }
            }
        }
    }

    private TextComponent generateMessage(Player p, String grupo, String rank, String money, String finalString, int prestigeLevel) {
        TextComponent message = new TextComponent(finalString);
        LanguageManager langManager = LanguageManager.getInstance();
        TextComponent[] hoverText;

        if (prestigeLevel > 0) {
            hoverText = new TextComponent[]{
                    new TextComponent("§6" + p.getName() + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.prestige", String.valueOf(prestigeLevel)) + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.role", grupo) + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.rank", rank) + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.money", money))
            };
        } else {
            hoverText = new TextComponent[]{
                    new TextComponent("§6" + p.getName() + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.role", grupo) + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.rank", rank) + "\n"),
                    new TextComponent(langManager.getMessage(p, "chat.hover.money", money))
            };
        }
        HoverEvent hoverEvent = new HoverEvent(HoverEvent.Action.SHOW_TEXT, hoverText);
        message.setHoverEvent(hoverEvent);
        return message;
    }


}
