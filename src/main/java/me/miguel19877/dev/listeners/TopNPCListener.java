package me.miguel19877.dev.listeners;

import me.miguel19877.dev.<PERSON>up;
import net.jitse.npclib.api.events.NPCInteractEvent;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;

public class TopNPCListener implements Listener {

    @EventHandler
    public void onNPCInteract(NPCInteractEvent event) {
        if (ChatColor.stripColor(event.getNPC().getPlayerLines(event.getWhoClicked()).get(0)).equalsIgnoreCase("Top Money")) {
            event.getWhoClicked().openInventory(Rankup.topMoneyInventory);
        }
        if (ChatColor.stripColor(event.getNPC().getPlayerLines(event.getWhoClicked()).get(0)).equalsIgnoreCase("Top Kills")) {
            event.getWhoClicked().openInventory(Rankup.topKillsInventory);
        }
        if (ChatColor.stripColor(event.getNPC().getPlayerLines(event.getWhoClicked()).get(0)).equalsIgnoreCase("Top Clan Kills")) {
            event.getWhoClicked().openInventory(Rankup.topClanKillsInventory);
        }
    }

    @EventHandler
    public void inventoryClick(InventoryClickEvent event) {
        if (event.getInventory().getName().equalsIgnoreCase("§a§lTop Money")) {
            event.setCancelled(true);
        }
        if (event.getInventory().getName().equalsIgnoreCase("§c§lTop Kills")) {
            event.setCancelled(true);
        }
        if (event.getInventory().getName().equalsIgnoreCase("§6§lTop Clan Kills")) {
            event.setCancelled(true);
        }
    }

}
