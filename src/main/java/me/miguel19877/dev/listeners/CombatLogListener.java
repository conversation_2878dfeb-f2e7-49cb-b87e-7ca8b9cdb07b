package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.CombatLogManager;
import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;

public class CombatLogListener implements Listener {
    
    private final CombatLogManager combatManager;
    
    public CombatLogListener() {
        this.combatManager = CombatLogManager.getInstance();
    }
    
    /**
     * Handle player vs player damage to trigger combat log
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDamage(EntityDamageByEntityEvent event) {
        Player victim = null;
        Player attacker = null;
        
        // Check if victim is a player
        if (event.getEntity() instanceof Player) {
            victim = (Player) event.getEntity();
        } else {
            return; // Not a player being damaged
        }
        
        // Check if attacker is a player (direct attack)
        if (event.getDamager() instanceof Player) {
            attacker = (Player) event.getDamager();
        }
        // Check if attacker is a player (projectile attack)
        else if (event.getDamager() instanceof Projectile) {
            Projectile projectile = (Projectile) event.getDamager();
            if (projectile.getShooter() instanceof Player) {
                attacker = (Player) projectile.getShooter();
            }
        }
        
        // If we don't have both players, don't trigger combat log
        if (attacker == null || victim == null) {
            return;
        }
        
        // Don't trigger combat log if players are the same (shouldn't happen but safety check)
        if (attacker.getUniqueId().equals(victim.getUniqueId())) {
            return;
        }
        
        // Only trigger combat log in specific worlds (based on existing PvP logic)
        String worldName = victim.getWorld().getName();

        // Check if PvP is allowed in this location (based on existing logic in JoinListener)
        if (worldName.equals("minas")) {
            // In mines world, check if in PvP area using the same logic as JoinListener
            int x = victim.getLocation().getBlockX();
            int z = victim.getLocation().getBlockZ();

            // Use the same PvP area check as in JoinListener - only allow combat log in PvP area
            if (!Rankup.minapvppvp.contains(x, 4, z)) {
                return; // Not in PvP area, don't trigger combat log
            }
        } else {
            // In other worlds, PvP might be disabled, so we don't trigger combat log
            // This follows the same logic as the existing PvP system
            return;
        }
        
        // Put both players in combat
        combatManager.putInCombat(victim);
        combatManager.putInCombat(attacker);
    }
    
    /**
     * Block all commands while in combat
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerCommand(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        
        // Check if player is in combat
        if (!combatManager.isInCombat(player)) {
            return; // Player not in combat, allow command
        }
        
        // Get the command (remove the leading slash and convert to lowercase)
        String command = event.getMessage().substring(1).toLowerCase();
        String[] args = command.split(" ");
        String baseCommand = args[0];
        
        // Allow certain emergency commands even during combat (optional)
        // You can add exceptions here if needed, for example:
        // if (baseCommand.equals("help") || baseCommand.equals("rules")) {
        //     return; // Allow these commands during combat
        // }
        
        // Block the command
        event.setCancelled(true);
        
        // Get remaining combat time
        int remainingTime = combatManager.getRemainingCombatTime(player);
        
        // Send combat block message
        String blockMessage = LanguageManager.getInstance().getMessage(player, "combat.command_blocked", String.valueOf(remainingTime));
        if (blockMessage == null) {
            blockMessage = "§c§lCOMBATE! §7Não podes usar comandos durante o combate! Tempo restante: §c" + remainingTime + "s";
        }
        player.sendMessage(blockMessage);
    }
}
