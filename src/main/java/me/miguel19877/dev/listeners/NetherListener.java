package me.miguel19877.dev.listeners;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityPortalEnterEvent;
import org.bukkit.event.player.PlayerPortalEvent;
import org.bukkit.event.world.PortalCreateEvent;

public class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Listener {

    // Deactivate the Nether portal creation event
    @EventHandler
    public void onPortalCreate(PortalCreateEvent event) {
        event.setCancelled(true);
    }

    // Disable the Nether portal pass-through event
    @EventHandler
    public void onPortalEnter(PlayerPortalEvent event) {
        event.setCancelled(true);
    }

}
