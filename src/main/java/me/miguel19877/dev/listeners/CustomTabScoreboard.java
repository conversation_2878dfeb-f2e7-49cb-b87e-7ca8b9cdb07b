package me.miguel19877.dev.listeners;

import fr.mrmicky.fastboard.FastBoard;
import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.Tasks.Evento;
import me.miguel19877.dev.managers.BlocksMinedManager;
import me.miguel19877.dev.managers.KillDeathManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.scoreboard.Team;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CustomTabScoreboard implements Listener {

    private final Scoreboard scoreboard;
    private final Map<UUID, FastBoard> boards = new HashMap<>();

    public CustomTabScoreboard() {
        scoreboard = Bukkit.getScoreboardManager().getNewScoreboard();
        createTeam("01", "§4[DONO] §f");
        createTeam("02", "§b[DEV] §f");
        createTeam("03", "§6[ADM] §f");
        createTeam("04", "§a[MOD] §f");
        createTeam("05", "§e[HELPER] §f");
        createTeam("06", "§c[uP] §f");
        createTeam("07", "§5[STREAMER] §f");
        createTeam("08", "§4[Y§fT] §f");
        createTeam("09", "§7[VIP GOD] §f");
        createTeam("10", "§7[VIP SUPER] §f");
        createTeam("11", "§7[VIP PRO] §f");
        createTeam("12", "§7[VIP+] §f");
        createTeam("13", "§7[VIP] §f");
        createTeam("14", "§8[Membro] §f");
        for (Player player : Bukkit.getOnlinePlayers()) {
            FastBoard board = new FastBoard(player);
            board.updateTitle("§6§lCraftAndHelps");
            boards.put(player.getUniqueId(), board);
            new BukkitRunnable() {
                @Override
                public void run() {
                    int groupId = Permissions.getGrupoId(player);
                    // Ensure groupId is within your defined range to prevent any out-of-bounds issues.
                    String teamName = String.format("%02d", 15 - groupId); // Pads with zero for single digits, inverting the groupId to teamName mapping.

                    assignPlayerToTeam(player, teamName);
                }
            }.runTaskLaterAsynchronously(Rankup.getInstance(), 60);
        }
        new BukkitRunnable() {
            @Override
            public void run() {
                for (FastBoard board : boards.values()) {
                    updateBoard(board);
                }
            }
        }.runTaskTimerAsynchronously(Rankup.getInstance(), 0, 40);
    }

    private void createTeam(String name, String prefix) {
        Team team = scoreboard.registerNewTeam(name);
        team.setPrefix(prefix);
    }

    private void assignPlayerToTeam(Player player, String teamName) {
        Team team = scoreboard.getTeam(teamName);
        if (team != null) {
            team.addEntry(player.getName());
            player.setScoreboard(scoreboard);
        }
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        // Task async to avoid lag, 20 ticks delay, it will fetch his group id, go by reverse so highest is the first, and then assign him to the team
        new BukkitRunnable() {
            @Override
            public void run() {
                Player p = event.getPlayer();
                int groupId = Permissions.getGrupoId(p);
                // Ensure groupId is within your defined range to prevent any out-of-bounds issues.
                String teamName = String.format("%02d", 15 - groupId); // Pads with zero for single digits, inverting the groupId to teamName mapping.

                assignPlayerToTeam(p, teamName);
            }
        }.runTaskLaterAsynchronously(Rankup.getInstance(), 20);
        FastBoard board = new FastBoard(event.getPlayer());
        board.updateTitle("§6§lCraftAndHelps");
        boards.put(event.getPlayer().getUniqueId(), board);
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        FastBoard board = boards.remove(event.getPlayer().getUniqueId());
        if (board != null) {
            board.delete();
        }
    }

    public static String convert(long value) {
        if (value < 1000) {
            return String.valueOf(value);
        }
        int exp = (int) (Math.log(value) / Math.log(1000));
        return String.format("%.1f%c", value / Math.pow(1000, exp), "kMBTQ".charAt(exp-1));
    }

    private void updateBoard(FastBoard board) {
        Player player = board.getPlayer();
        LanguageManager langManager = LanguageManager.getInstance();
        int rankid = RankSystem.getRankId(player);

        board.updateLines(
                "",
                " " + langManager.getMessage(player, "scoreboard.nick", player.getDisplayName()),
                " " + langManager.getMessage(player, "scoreboard.rank", RankSystem.getRankName(rankid).replace("[", "").replace("]", "")),
                " " + langManager.getMessage(player, "scoreboard.money", convert(Economy.getMoney(player.getUniqueId()))),
                "",
                " " + langManager.getMessage(player, "scoreboard.online", String.valueOf(Bukkit.getOnlinePlayers().size())),
                "",
                " " + langManager.getMessage(player, "scoreboard.kills", String.valueOf(KillDeathManager.getKills(player))),
                " " + langManager.getMessage(player, "scoreboard.deaths", String.valueOf(KillDeathManager.getDeaths(player))),
                "",
                " " + langManager.getMessage(player, "scoreboard.blocks", convert(BlocksMinedManager.getBlocksMined(player))),
                "",
                " " + langManager.getMessage(player, "scoreboard.time", DateTimeFormatter.ofPattern("HH:mm").format(LocalDateTime.now(ZoneId.of("Europe/Lisbon")))),
                "",
                langManager.getMessage(player, "scoreboard.website")
        );
    }

    /*private void displayScoreboardInfo(Player p, Scoreboard playerScoreboard) {
        p.setScoreboard(playerScoreboard);
        String objectiveId = "Rankup";
        if (p.getScoreboard().getObjective(objectiveId) == null) {
            Objective objective = p.getScoreboard().registerNewObjective(objectiveId, "dummy");
            objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        }
        p.getScoreboard().getObjective(objectiveId).setDisplayName("§6§lCraftAndHelps");
        p.getScoreboard().getObjective(objectiveId).getScore("§7Site: growup.gg").setScore(1);
        p.getScoreboard().getObjective(objectiveId).getScore("§b").setScore(3);
        p.getScoreboard().getObjective(objectiveId).getScore("§c").setScore(5);
        p.getScoreboard().getObjective(objectiveId).getScore("§d").setScore(9);
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
        new BukkitRunnable() {
            @Override
            public void run() {
                if (p.isOnline()) {

                    for (String str : p.getScoreboard().getEntries()) {
                        if (str.contains("Dinheiro:") || str.contains("Rank:") || str.contains("Nick:") || str.contains("Hora:") || str.contains("Online:")) {
                            p.getScoreboard().resetScores(str);
                        }
                    }
                        String coins = convert(Economy.getMoney(p));
                        String rank = RankSystem.getGrupo(p);
                        //Remove [] from string
                        rank = rank.substring(1, rank.length() - 1);
                        //Get system time as HH:MM
                        LocalDateTime now = LocalDateTime.now();
                        String time = dtf.format(now);
                        p.getScoreboard().getObjective(objectiveId).getScore("  §7Hora: " + time).setScore(2);
                        p.getScoreboard().getObjective(objectiveId).getScore("§b").setScore(3);
                        p.getScoreboard().getObjective(objectiveId).getScore("  §bOnline: " + Bukkit.getServer().getOnlinePlayers().size()).setScore(4);
                        p.getScoreboard().getObjective(objectiveId).getScore("§c").setScore(5);
                        p.getScoreboard().getObjective(objectiveId).getScore("  §cDinheiro: " + coins + "€").setScore(6);
                        p.getScoreboard().getObjective(objectiveId).getScore("  §dRank: " + rank).setScore(7);
                        p.getScoreboard().getObjective(objectiveId).getScore("  §aNick: " + p.getDisplayName()).setScore(8);
                        p.getScoreboard().getObjective(objectiveId).getScore("§d").setScore(9);
                } else {
                    this.cancel();
                }
            }
        }.runTaskTimerAsynchronously(Rankup.getInstance(), 40, 40);
    }*/

}
