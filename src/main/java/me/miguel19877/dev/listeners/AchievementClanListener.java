package me.miguel19877.dev.listeners;

import me.miguel19877.dev.managers.ClanManager;
import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.entity.Player;

/**
 * Handles clan-related achievement tracking
 */
public class AchievementClanListener {
    
    /**
     * Called when a player joins a clan
     */
    public static void onPlayerJoinClan(Player player) {
        // recruta_de_clan - Join a clan for the first time
        AchievementHelper.setProgress(player, "recruta_de_clan", 1);
    }
    
    /**
     * Called when a player leaves a clan
     */
    public static void onPlayerLeaveClan(Player player) {
        // Currently no achievements for leaving clans
    }
    
    /**
     * Called when a player creates a clan
     */
    public static void onPlayer<PERSON>reate<PERSON>lan(Player player) {
        // recruta_de_clan - Creating a clan counts as joining one
        AchievementHelper.setProgress(player, "recruta_de_clan", 1);
    }
}
