package me.miguel19877.dev.listeners;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.*;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.messaging.PluginMessageListener;

import java.nio.charset.StandardCharsets;

public class PlayerDataListener implements PluginMessageListener {

    private final Gson gson = new Gson();

    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        if (!channel.equals("cah:playerdata")) {
            return;
        }

        try {
            String dataString = new String(message, StandardCharsets.UTF_8);
            // Parse data into our DTO
            PlayerData data = gson.fromJson(dataString, PlayerData.class);

            // Store the data, waiting for PlayerJoinEvent to process it.
            Rankup.getInstance().pendingPlayerData.put(player.getUniqueId(), data);

            // Log for debugging
            Bukkit.getLogger().info("Received and cached data for " + player.getName() + ". Awaiting player join to apply.");

            // IMPORTANT: Now we check if the player is *already* online.
            // This handles the case where PlayerJoinEvent fired before the message arrived.
            if (player.isOnline()) {
                applyPlayerData(player);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void applyPlayerData(Player player) {
        // Retrieve and remove the data in one atomic operation to prevent re-applying it.
        PlayerData data = Rankup.getInstance().pendingPlayerData.remove(player.getUniqueId());

        if (data != null) {

            // --- ALL YOUR DATA APPLICATION LOGIC GOES HERE ---
            Economy.setMoney(player.getUniqueId(), data.getMoney());
            Economy.setAlmas(player.getUniqueId(), data.getAlmas());
            BlocksMinedManager.setBlocksMined(player, data.getBlocksMined());
            KillDeathManager.setKills(player, (int) data.getKills());
            KillDeathManager.setDeaths(player, (int) data.getDeaths());
            RankSystem.setRank(player, data.getRankId());
            PrestigeManager.setPrestigeLevel(player, data.getPrestige());
            Permissions.setGrupo(player, data.getgroupId());
            VIPManager.setTime(player.getUniqueId(), data.getVipExpiration());

            // Apply language preference
            if (data.getLanguage() != null) {
                LanguageManager.getInstance().setPlayerLanguage(player.getUniqueId(), data.getLanguage());
            }

            // Apply Twitch channel data
            if (data.getTwitchChannel() != null && !data.getTwitchChannel().isEmpty()) {
                TwitchManager.setTwitchChannel(player.getUniqueId(), data.getTwitchChannel());
            }
            // etc.
        }
        // If data is null, it means either:
        // 1. The plugin message hasn't arrived yet (it will trigger this method again).
        // 2. There was no data to send from Velocity.
        // Both are perfectly normal scenarios.
    }
}