package me.miguel19877.dev.listeners;

import me.miguel19877.dev.KitManager;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.Calendar;
import java.util.Date;

public class InventoryClic implements Listener {


    @EventHandler
    public void onInventoryClick(InventoryClickEvent e) {
        if (e.getInventory() != null && e.getCurrentItem() != null && e.getClickedInventory() != null && e.getCurrentItem().getItemMeta() != null) {
            if (e.getInventory().getName().equals(Rankup.kitInventory.getName())) {
                e.setCancelled(true);
                Player p = (Player) e.getWhoClicked();
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lKit Rank")) {
                    p.closeInventory();
                    boolean cooldownExpired = KitManager.isCooldownExpired(p);
                    if (cooldownExpired) {
                        ItemStack[] items = KitManager.getKit(p, "rank");
                        giveItems(p, items);
                        p.updateInventory();
                    } else {
                        long timeLeft = KitManager.getCooldown(p) - System.currentTimeMillis();
                        long hours = timeLeft / (1000 * 60 * 60);
                        long minutes = (timeLeft % (1000 * 60 * 60)) / (1000 * 60);
                        long seconds = (timeLeft % (1000 * 60)) / 1000;
                        LanguageManager.getInstance().sendMessage(p, "kit.cooldown", String.valueOf(hours), String.valueOf(minutes), String.valueOf(seconds));
                    }
                }else if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lKit VIP")) {
                    p.closeInventory();
                    int grupoid = Permissions.getGrupoId(p);
                    if (grupoid >= 2) {
                        boolean cooldownExpired = KitManager.isCooldownExpired(p);
                        if (cooldownExpired) {
                            ItemStack[] items = KitManager.getKit(p, "vip");
                            giveItems(p, items);
                            p.updateInventory();
                        } else {
                            long timeLeft = KitManager.getCooldown(p) - System.currentTimeMillis();
                            long hours = timeLeft / (1000 * 60 * 60);
                            long minutes = (timeLeft % (1000 * 60 * 60)) / (1000 * 60);
                            long seconds = (timeLeft % (1000 * 60)) / 1000;
                            LanguageManager.getInstance().sendMessage(p, "kit.cooldown", String.valueOf(hours), String.valueOf(minutes), String.valueOf(seconds));
                        }
                    } else {
                        LanguageManager.getInstance().sendMessage(p, "kit.no_permission");
                    }
                } else if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lKit PvP")) {
                    p.closeInventory();
                    givekitPvP(p);
                    p.updateInventory();
                    LanguageManager.getInstance().sendMessage(p, "kit.pvp_received");
                }
            }
        }
    }

    private void giveItems(Player player, ItemStack[] items) {
        // Give the player the items in the array
        for (ItemStack item : items) {
            if (item != null) {
                player.getInventory().addItem(item);
            }
        }
    }

    private void givekitPvP(Player p) {
        //full chainmail armor, iron sword, bow and 32 arrows
        p.getInventory().addItem(new ItemStack(Material.CHAINMAIL_BOOTS));
        p.getInventory().addItem(new ItemStack(Material.CHAINMAIL_LEGGINGS));
        p.getInventory().addItem(new ItemStack(Material.CHAINMAIL_CHESTPLATE));
        p.getInventory().addItem(new ItemStack(Material.CHAINMAIL_HELMET));
        p.getInventory().addItem(new ItemStack(Material.IRON_SWORD));
        p.getInventory().addItem(new ItemStack(Material.IRON_PICKAXE));
        p.getInventory().addItem(new ItemStack(Material.BOW));
        p.getInventory().addItem(new ItemStack(Material.ARROW, 32));
    }
}
