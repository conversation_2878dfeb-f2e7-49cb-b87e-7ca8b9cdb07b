package me.miguel19877.dev.events;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.PotionMeta;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.potion.Potion;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.potion.PotionType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

public class PvPEvent {
    private static PvPEvent instance;
    private final JavaPlugin plugin;
    private boolean isEventActive = false;
    private boolean isEntryPhase = false;
    private BukkitTask entryTimer;
    private BukkitTask countdownTimer;
    private Location spawnLocation1;
    private Location spawnLocation2;
    private Location lobbyLocation;
    private final List<Player> participants = new ArrayList<>();
    private final List<Player> finalPositions = new ArrayList<>();
    private final Queue<PvPMatch> matchQueue = new LinkedList<>();
    private PvPMatch currentMatch;
    private final Map<UUID, ItemStack[]> savedInventories = new HashMap<>();
    private final Map<UUID, ItemStack[]> savedArmor = new HashMap<>();

    private PvPEvent(JavaPlugin plugin) {
        this.plugin = plugin;
    }

    public static PvPEvent getInstance(JavaPlugin plugin) {
        if (instance == null) {
            instance = new PvPEvent(plugin);
        }
        return instance;
    }

    public void startEvent() {
        if (isEventActive) {
            return;
        }

        isEventActive = true;
        isEntryPhase = true;
        participants.clear();
        finalPositions.clear();
        matchQueue.clear();
        currentMatch = null;

        // Start 5-minute entry timer
        entryTimer = new BukkitRunnable() {
            int timeLeft = 300; // 5 minutes in seconds

            @Override
            public void run() {
                if (timeLeft <= 0) {
                    if (participants.size() < 2) {
                        plugin.getServer().broadcastMessage("§cO evento foi cancelado por não ter participantes suficientes!");
                        endEvent();
                        return;
                    }
                    startTournament();
                    cancel();
                    return;
                }

                if (timeLeft % 60 == 0) { // Every minute
                    plugin.getServer().broadcastMessage("§6§lTorneio 1v1 PvP §e- §6" + (timeLeft / 60) + " minutos restantes para te juntares!");
                }

                timeLeft--;
            }
        }.runTaskTimer(plugin, 0L, 20L);
    }

    private void startTournament() {
        isEntryPhase = false;
        plugin.getServer().broadcastMessage("§6§lFase de inscrição terminada! §eO torneio vai começar!");
        createMatches();
    }

    public void addParticipant(Player player) {
        if (!isEventActive || !isEntryPhase || participants.contains(player)) {
            return;
        }
        participants.add(player);
        player.sendMessage("§aEstás registado para o torneio");
        plugin.getServer().broadcastMessage("§6§lTorneio 1v1 PvP: §e" + player.getName() + " entrou! (" + participants.size() + " jogadores inscritos)");
        savePlayerInventory(player);
        giveBattleKit(player);
    }

    private void createMatches() {
        Collections.shuffle(participants);
        matchQueue.clear();

        // Handle odd number of participants
        if (participants.size() % 2 != 0) {
            Player byePlayer = participants.remove(participants.size() - 1);
            matchQueue.add(new PvPMatch(byePlayer, null));
            byePlayer.sendMessage("§aRecebeste um passe para a próxima ronda!");
        }

        // Create matches
        for (int i = 0; i < participants.size(); i += 2) {
            matchQueue.add(new PvPMatch(participants.get(i), participants.get(i + 1)));
        }

        startNextMatch();
    }

    private void startNextMatch() {
        if (matchQueue.isEmpty()) {
            if (participants.size() == 1) {
                endEvent();
            } else {
                createMatches();
            }
            return;
        }

        currentMatch = matchQueue.poll();
        Player player1 = currentMatch.getPlayer1();
        Player player2 = currentMatch.getPlayer2();

        if (player2 == null) {
            // Handle bye
            player1.sendMessage("§aRecebeste um passe para a próxima ronda!");
            startNextMatch();
            return;
        }

        plugin.getServer().broadcastMessage("§6§lPróximo combate: §e" + player1.getName() + " §6vs §e" + player2.getName());

        // Start countdown
        countdownTimer = new BukkitRunnable() {
            int count = 5;

            @Override
            public void run() {
                if (count <= 0) {
                    player1.teleport(spawnLocation1);
                    player2.teleport(spawnLocation2);
                    player1.sendMessage("§aO combate começou!");
                    player2.sendMessage("§aO combate começou!");
                    giveBattleKit(player1);
                    giveBattleKit(player2);
                    cancel();
                    return;
                }

                player1.sendMessage("§eO combate começa em " + count + "...");
                player2.sendMessage("§eO combate começa em " + count + "...");
                count--;
            }
        }.runTaskTimer(plugin, 0L, 20L);
    }

    public void handlePlayerDeath(Player victim) {
        if (!isEventActive || currentMatch == null) {
            return;
        }

        Player winner = currentMatch.getPlayer1().equals(victim) ? currentMatch.getPlayer2() : currentMatch.getPlayer1();
        if (winner == null) {
            return;
        }

        winner.teleport(getLobbyLocation());

        plugin.getServer().broadcastMessage("§6§l" + winner.getName() + " §evenceu o combate contra §6§l" + victim.getName() + "§e!");
        participants.remove(victim);
        finalPositions.add(0, victim); // Add to the beginning of final positions
        startNextMatch();
        restorePlayerInventory(victim);
    }

    private void endEvent() {
        isEventActive = false;
        isEntryPhase = false;
        currentMatch = null;

        if (entryTimer != null) {
            entryTimer.cancel();
        }
        if (countdownTimer != null) {
            countdownTimer.cancel();
        }

        if (participants.isEmpty() && finalPositions.isEmpty()) {
            plugin.getServer().broadcastMessage("§cO evento terminou sem vencedores!");
            return;
        }

        // Add the last remaining player to final positions
        if (!participants.isEmpty()) {
            finalPositions.add(0, participants.get(0));
        }

        // Announce winners
        Player firstPlace = finalPositions.size() > 0 ? finalPositions.get(0) : null;
        Player secondPlace = finalPositions.size() > 1 ? finalPositions.get(1) : null;
        Player thirdPlace = finalPositions.size() > 2 ? finalPositions.get(2) : null;
        plugin.getServer().broadcastMessage("");
        plugin.getServer().broadcastMessage("§6§lTorneio 1v1 PvP Terminado!");
        if (firstPlace != null) {
            plugin.getServer().broadcastMessage("§6§l1º Lugar: §e" + firstPlace.getName());
        }
        if (secondPlace != null) {
            plugin.getServer().broadcastMessage("§6§l2º Lugar: §e" + secondPlace.getName());
        }
        if (thirdPlace != null) {
            plugin.getServer().broadcastMessage("§6§l3º Lugar: §e" + thirdPlace.getName());
        }
        plugin.getServer().broadcastMessage("");

        // Teleport all participants to lobby
        for (Player participant : finalPositions) {
            if (lobbyLocation != null) {
                participant.teleport(lobbyLocation);
            }
        }

        participants.clear();
        finalPositions.clear();
        matchQueue.clear();
    }

    // Location setters
    public void setSpawnLocation1(Location location) {
        this.spawnLocation1 = location;
    }

    public void setSpawnLocation2(Location location) {
        this.spawnLocation2 = location;
    }

    public void setLobbyLocation(Location location) {
        this.lobbyLocation = location;
    }

    // Getters
    public boolean isEventActive() {
        return isEventActive;
    }

    public boolean isEntryPhase() {
        return isEntryPhase;
    }

    public Location getLobbyLocation() {
        return lobbyLocation;
    }

    public void savePlayerInventory(Player player) {
        savedInventories.put(player.getUniqueId(), player.getInventory().getContents());
        savedArmor.put(player.getUniqueId(), player.getInventory().getArmorContents());
    }

    public void restorePlayerInventory(Player player) {
        ItemStack[] inventory = savedInventories.remove(player.getUniqueId());
        ItemStack[] armor = savedArmor.remove(player.getUniqueId());

        if (inventory != null) {
            player.getInventory().setContents(inventory);
        }
        if (armor != null) {
            player.getInventory().setArmorContents(armor);
        }
    }

    public void giveBattleKit(Player player) {
        // Clear current inventory
        player.getInventory().clear();

        player.getInventory().addItem(new ItemStack(Material.DIAMOND_SWORD));
        ItemStack bow = new ItemStack(Material.BOW, 1);
        bow.addEnchantment(Enchantment.ARROW_DAMAGE, 1);
        player.getInventory().addItem(bow);
        player.getInventory().addItem(new ItemStack(Material.ARROW, 32));
        Potion healthPotionType = new Potion(PotionType.INSTANT_HEAL, 1);
        ItemStack healthPotion = healthPotionType.toItemStack(2); // Create 2 potions of this type
        player.getInventory().addItem(healthPotion);

        // --- Strength Potion ---
        // PotionType.STRENGTH, Level 1 (I), Duration default (3:00)
        Potion strengthPotionType = new Potion(PotionType.STRENGTH, 1);
        // You can also set properties like splash:
        // strengthPotionType.setSplash(true);
        ItemStack strengthPotion = strengthPotionType.toItemStack(1); // Create 1 potion
        player.getInventory().addItem(strengthPotion);
        player.getInventory().setHelmet(new ItemStack(Material.DIAMOND_HELMET));
        player.getInventory().setChestplate(new ItemStack(Material.DIAMOND_CHESTPLATE));
        player.getInventory().setLeggings(new ItemStack(Material.DIAMOND_LEGGINGS));
        player.getInventory().setBoots(new ItemStack(Material.DIAMOND_BOOTS));
    }

    private static class PvPMatch {
        private final Player player1;
        private final Player player2;

        public PvPMatch(Player player1, Player player2) {
            this.player1 = player1;
            this.player2 = player2;
        }

        public Player getPlayer1() {
            return player1;
        }

        public Player getPlayer2() {
            return player2;
        }
    }
}