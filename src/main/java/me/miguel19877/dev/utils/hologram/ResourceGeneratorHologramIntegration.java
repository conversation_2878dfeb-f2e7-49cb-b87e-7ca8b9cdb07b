package me.miguel19877.dev.utils.hologram;

import me.miguel19877.dev.bedwars.ResourceGenerator;
import me.miguel19877.dev.bedwars.ResourceManager;
import me.miguel19877.dev.bedwars.ResourceType;
import me.miguel19877.dev.bedwars.BedWarsGame;
import org.bukkit.Location;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import me.miguel19877.dev.Main;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Integrates hologram displays with resource generators to show countdown timers.
 * Manages hologram lifecycle for Diamond and Emerald generators specifically.
 */
public class ResourceGeneratorHologramIntegration {

    private final HologramManager hologramManager;
    private final Map<String, String> generatorHolograms; // generatorId -> hologramId
    private final Map<String, ResourceGenerator> activeGenerators; // generatorId -> generator instance
    private final Logger logger;
    private BukkitTask updateTask;
    private boolean active;

    public ResourceGeneratorHologramIntegration() {
        this.hologramManager = HologramManager.getInstance();
        this.generatorHolograms = new HashMap<>();
        this.activeGenerators = new HashMap<>();
        this.logger = Main.getInstance().getLogger();
        this.active = false;
    }

    /**
     * Sets up holograms for all Diamond and Emerald generators in the ResourceManager.
     * Only creates holograms for Diamond and Emerald generators as per requirements.
     *
     * @param resourceManager The ResourceManager containing generators
     */
    public void setupGeneratorHolograms(ResourceManager resourceManager) {
        if (resourceManager == null) {
            logger.warning("Cannot setup generator holograms: ResourceManager is null");
            return;
        }

        logger.info("Setting up generator holograms...");

        // Clear existing holograms first
        cleanup();

        // Setup holograms for Diamond generators
        for (ResourceGenerator generator : resourceManager.getGeneratorsByType(ResourceType.DIAMOND)) {
            createGeneratorHologram(generator);
        }

        // Setup holograms for Emerald generators
        for (ResourceGenerator generator : resourceManager.getGeneratorsByType(ResourceType.EMERALD)) {
            createGeneratorHologram(generator);
        }

        // Start the update task if we have any holograms
        if (!generatorHolograms.isEmpty()) {
            startUpdateTask();
            active = true;
            logger.info("Created " + generatorHolograms.size() + " generator holograms");
        }
    }

    /**
     * Creates a hologram for a specific resource generator.
     *
     * @param generator The resource generator to create a hologram for
     */
    private void createGeneratorHologram(ResourceGenerator generator) {
        if (generator == null || generator.getLocation() == null) {
            return;
        }

        String generatorId = generator.getId();
        String hologramId = "gen_" + generatorId;

        // Position hologram above the generator location
        Location hologramLocation = generator.getLocation().clone();

        // Create initial text
        String initialText = formatCountdownText(generator.getResourceType(), generator.getCurrentSpawnInterval());

        try {
            Hologram hologram = hologramManager.createHologram(hologramId, hologramLocation, initialText, 0.0, true);
            if (hologram != null) {
                generatorHolograms.put(generatorId, hologramId);
                activeGenerators.put(generatorId, generator);
                logger.fine("Created hologram for generator: " + generatorId);
            }
        } catch (Exception e) {
            logger.warning("Failed to create hologram for generator " + generatorId + ": " + e.getMessage());
        }
    }

    /**
     * Updates the hologram text for a specific generator with countdown timer.
     *
     * @param generatorId The ID of the generator
     * @param timeRemaining Time remaining until next spawn in seconds
     */
    public void updateGeneratorHologram(String generatorId, int timeRemaining) {
        String hologramId = generatorHolograms.get(generatorId);
        if (hologramId == null) {
            return;
        }

        Hologram hologram = hologramManager.getHologram(hologramId);
        if (hologram == null || !hologram.isValid()) {
            // Hologram was removed or became invalid, clean up our reference
            generatorHolograms.remove(generatorId);
            activeGenerators.remove(generatorId);
            return;
        }

        // Determine resource type from generator ID
        ResourceType resourceType = getResourceTypeFromGeneratorId(generatorId);
        if (resourceType == null) {
            return;
        }

        String countdownText = formatCountdownText(resourceType, timeRemaining);
        hologram.setText(countdownText);
    }

    /**
     * Removes the hologram associated with a specific generator.
     *
     * @param generatorId The ID of the generator
     */
    public void removeGeneratorHologram(String generatorId) {
        String hologramId = generatorHolograms.remove(generatorId);
        activeGenerators.remove(generatorId);

        if (hologramId != null) {
            hologramManager.removeHologram(hologramId);
            logger.fine("Removed hologram for generator: " + generatorId);
        }
    }

    /**
     * Starts the periodic update task for hologram countdown timers.
     */
    private void startUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
        }

        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAllHolograms();
            }
        }.runTaskTimer(Main.getInstance(), 20L, 10L); // Update every second

        logger.fine("Started hologram update task");
    }

    /**
     * Updates all generator holograms with current countdown timers.
     */
    private void updateAllHolograms() {
        if (!active || generatorHolograms.isEmpty()) {
            return;
        }

        for (Map.Entry<String, String> entry : generatorHolograms.entrySet()) {
            String generatorId = entry.getKey();
            ResourceGenerator generator = activeGenerators.get(generatorId);

            if (generator == null || !generator.isActive()) {
                continue;
            }

            // Get time remaining from the generator itself
            int timeRemaining = generator.getTimeUntilNextSpawn();

            updateGeneratorHologram(generatorId, timeRemaining);
        }
    }

    /**
     * Formats the countdown text for display on holograms.
     *
     * @param resourceType The type of resource being generated
     * @param timeRemaining Time remaining in seconds
     * @return Formatted countdown text
     */
    private String formatCountdownText(ResourceType resourceType, int timeRemaining) {
        if (timeRemaining <= 0) {
            return resourceType.getColor() + "§l" + resourceType.getDisplayName() + " spawns now!";
        } else {
            return resourceType.getColor() + "§l" + resourceType.getDisplayName() + " spawns in " + timeRemaining + "s";
        }
    }

    /**
     * Determines the resource type from a generator ID.
     *
     * @param generatorId The generator ID
     * @return The ResourceType, or null if not determinable
     */
    private ResourceType getResourceTypeFromGeneratorId(String generatorId) {
        if (generatorId.toLowerCase().contains("diamond")) {
            return ResourceType.DIAMOND;
        } else if (generatorId.toLowerCase().contains("emerald")) {
            return ResourceType.EMERALD;
        }
        return null;
    }

    /**
     * Checks if the integration is currently active.
     *
     * @return true if active, false otherwise
     */
    public boolean isActive() {
        return active;
    }

    /**
     * Gets the number of active generator holograms.
     *
     * @return The count of active holograms
     */
    public int getActiveHologramCount() {
        return generatorHolograms.size();
    }

    /**
     * Stops the integration and cleans up all generator holograms.
     */
    public void cleanup() {
        active = false;

        // Stop update task
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }

        // Remove all holograms
        for (String hologramId : generatorHolograms.values()) {
            hologramManager.removeHologram(hologramId);
        }

        generatorHolograms.clear();
        activeGenerators.clear();

        logger.info("ResourceGeneratorHologramIntegration cleanup completed");
    }

    /**
     * Starts hologram timers when the BedWars game enters IN_GAME state.
     * This method should be called when the game transitions to IN_GAME.
     *
     * @param game The BedWarsGame instance that is starting
     */
    public void onGameStart(BedWarsGame game) {
        if (game == null || game.getGameState() != BedWarsGame.GameState.IN_GAME) {
            logger.warning("Cannot start hologram timers: Game is not in IN_GAME state");
            return;
        }

        logger.info("Starting hologram timers for game: " + game.getGameId());

        // Setup holograms for the game's resource manager
        ResourceManager resourceManager = game.getResourceManager();
        if (resourceManager != null) {
            setupGeneratorHolograms(resourceManager);
        }
    }

    /**
     * Stops and cleans up holograms when the BedWars game ends.
     * This method should be called when the game transitions to ENDING state.
     *
     * @param game The BedWarsGame instance that is ending
     */
    public void onGameEnd(BedWarsGame game) {
        if (game == null) {
            logger.warning("Cannot stop hologram timers: Game is null");
            return;
        }

        logger.info("Stopping hologram timers for game: " + game.getGameId());

        // Cleanup all holograms
        cleanup();
    }

    /**
     * Handles hologram updates when generator levels change.
     * This method should be called when generators are upgraded.
     *
     * @param resourceManager The ResourceManager with updated generators
     */
    public void onGeneratorLevelChange(ResourceManager resourceManager) {
        if (resourceManager == null || !active) {
            return;
        }

        logger.info("Updating holograms due to generator level changes");

        // Refresh all holograms to reflect new spawn intervals
        for (Map.Entry<String, ResourceGenerator> entry : activeGenerators.entrySet()) {
            String generatorId = entry.getKey();
            ResourceGenerator generator = entry.getValue();

            if (generator != null && generator.isActive()) {
                // Update hologram with new spawn interval
                String hologramId = generatorHolograms.get(generatorId);
                if (hologramId != null) {
                    Hologram hologram = hologramManager.getHologram(hologramId);
                    if (hologram != null && hologram.isValid()) {
                        // Update with current spawn interval (level may have changed)
                        String updatedText = formatCountdownText(generator.getResourceType(), generator.getCurrentSpawnInterval());
                        hologram.setText(updatedText);
                    }
                }
            }
        }
    }

    /**
     * Gets information about the current state of generator holograms.
     *
     * @return Information string for debugging
     */
    public String getInfo() {
        return String.format("ResourceGeneratorHologramIntegration - Active: %s, Holograms: %d",
                active, generatorHolograms.size());
    }
}