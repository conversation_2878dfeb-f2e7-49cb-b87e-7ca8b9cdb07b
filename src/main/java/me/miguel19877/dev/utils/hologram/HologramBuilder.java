package me.miguel19877.dev.utils.hologram;

import org.bukkit.Location;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Builder class for creating holograms using a fluent API pattern.
 * Provides method chaining for easy hologram configuration and validation.
 */
public class HologramBuilder {
    
    private String id;
    private Location location;
    private String text;
    private List<String> lines;
    private double heightOffset = 0.0;
    private boolean visible = true;
    
    /**
     * Sets the unique identifier for the hologram.
     * 
     * @param id Unique identifier (required)
     * @return This builder instance for method chaining
     */
    public HologramBuilder id(String id) {
        this.id = id;
        return this;
    }
    
    /**
     * Sets the location where the hologram should appear.
     * 
     * @param location Base location for the hologram (required)
     * @return This builder instance for method chaining
     */
    public HologramBuilder location(Location location) {
        this.location = location;
        return this;
    }
    
    /**
     * Sets the text to display in the hologram.
     * 
     * @param text Text to display (required)
     * @return This builder instance for method chaining
     */
    public HologramBuilder text(String text) {
        this.text = text;
        this.lines = null; // Clear lines if text is set
        return this;
    }
    
    /**
     * Sets multiple lines of text to display in the hologram.
     * 
     * @param lines List of text lines to display (required)
     * @return This builder instance for method chaining
     */
    public HologramBuilder lines(List<String> lines) {
        this.lines = lines != null ? new ArrayList<>(lines) : null;
        this.text = null; // Clear single text if lines are set
        return this;
    }
    
    /**
     * Sets multiple lines of text to display in the hologram.
     * 
     * @param lines Array of text lines to display (required)
     * @return This builder instance for method chaining
     */
    public HologramBuilder lines(String... lines) {
        this.lines = lines != null ? Arrays.asList(lines) : null;
        this.text = null; // Clear single text if lines are set
        return this;
    }
    
    /**
     * Sets the vertical offset from the base location.
     * 
     * @param heightOffset Offset in blocks (default: 0.0)
     * @return This builder instance for method chaining
     */
    public HologramBuilder heightOffset(double heightOffset) {
        this.heightOffset = heightOffset;
        return this;
    }
    
    /**
     * Sets whether the hologram should be visible initially.
     * 
     * @param visible Visibility state (default: true)
     * @return This builder instance for method chaining
     */
    public HologramBuilder visible(boolean visible) {
        this.visible = visible;
        return this;
    }
    
    /**
     * Builds and creates the hologram with the configured parameters.
     * Validates all required parameters before creation.
     * 
     * @return A new Hologram instance
     * @throws IllegalArgumentException if required parameters are missing or invalid
     */
    public Hologram build() {
        validateParameters();
        
        // Create multi-line hologram if lines are specified
        if (lines != null && !lines.isEmpty()) {
            return new Hologram(id, location, lines, heightOffset, visible);
        } else {
            // Create single-line hologram
            return new Hologram(id, location, text, heightOffset, visible);
        }
    }
    
    /**
     * Validates that all required parameters are set and valid.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    private void validateParameters() {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("Hologram ID cannot be null or empty");
        }
        
        if (location == null) {
            throw new IllegalArgumentException("Hologram location cannot be null");
        }
        
        if (location.getWorld() == null) {
            throw new IllegalArgumentException("Hologram location must have a valid world");
        }
        
        // Validate that either text or lines is provided
        if (text == null && (lines == null || lines.isEmpty())) {
            throw new IllegalArgumentException("Hologram must have either text or lines specified");
        }
        
        // Validate lines if provided
        if (lines != null && !lines.isEmpty()) {
            for (String line : lines) {
                if (line == null) {
                    throw new IllegalArgumentException("Hologram lines cannot contain null values");
                }
            }
            
            // Limit number of lines to prevent performance issues
            if (lines.size() > 10) {
                throw new IllegalArgumentException("Hologram cannot have more than 10 lines");
            }
        }
        
        // Validate height offset is reasonable
        if (heightOffset < -10.0 || heightOffset > 10.0) {
            throw new IllegalArgumentException("Height offset must be between -10.0 and 10.0 blocks");
        }
    }
    
    /**
     * Creates a new builder instance for method chaining.
     * 
     * @return A new HologramBuilder instance
     */
    public static HologramBuilder create() {
        return new HologramBuilder();
    }
}