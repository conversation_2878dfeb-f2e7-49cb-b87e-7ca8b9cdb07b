package me.miguel19877.dev.utils;

import com.github.unldenis.hologram.Hologram;
import com.github.unldenis.hologram.HologramPool;
import com.github.unldenis.hologram.TextLine;
import com.github.unldenis.hologram.event.PlayerHologramHideEvent;
import com.github.unldenis.hologram.event.PlayerHologramInteractEvent;
import com.github.unldenis.hologram.event.PlayerHologramShowEvent;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;

import java.util.ArrayList;

public class ExampleHolograms implements Listener {

    private final Plugin plugin;
    private final HologramPool hologramPool;
    private final ArrayList<Hologram> holograms = new ArrayList<>();
    /**
     * @param plugin The plugin which uses the lib
     */
    public ExampleHolograms(Plugin plugin) {
        this.plugin = plugin;
        this.hologramPool = new HologramPool(plugin, 70, 0.5f, 5f);
    }


    public void deactivateHolograms() {
        for(Hologram holo : holograms) {
            hologramPool.remove(holo);
        }
    }

    public void deactivateHologram(Hologram holo) {
        if (holograms.contains(holo)) {
            hologramPool.remove(holo);
            holograms.remove(holo);
        }
    }

    public void appendHOLO(Location location, String text) {
        // building the NPC
        Hologram hologram = Hologram.builder()
                .location(location)
                .addLine(text, false)
                .build(hologramPool);
        holograms.add(hologram);
    }

    public Hologram appendHOLO2(Location location, String text) {
        // building the NPC
        Hologram hologram = Hologram.builder()
                .location(location)
                .addLine(text, false)
                .build(hologramPool);
        holograms.add(hologram);
        return hologram;
    }
    @EventHandler
    public void onHologramShow(PlayerHologramShowEvent event) {
        Hologram holo = event.getHologram();
        Player player = event.getPlayer();
    }

    /**
     * Doing something when a Hologram is hidden for a certain player.
     * @param event The event instance
     */
    @EventHandler
    public void onHologramHide(PlayerHologramHideEvent event) {
        Hologram holo = event.getHologram();
        Player player = event.getPlayer();
    }

    /**
     * Doing something when a Hologram is left-clicked by a certain player.
     * @param e The event instance
     */
    @EventHandler
    public void onHologramInteract(PlayerHologramInteractEvent e) {
        Player player = e.getPlayer();
        TextLine line = e.getLine();
        player.sendMessage("Click at " + line.parse(player));
    }
}