package me.miguel19877.dev.utils;

import me.miguel19877.dev.utils.hologram.Hologram;
import me.miguel19877.dev.utils.hologram.HologramManager;
import me.miguel19877.dev.utils.hologram.HologramBuilder;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class ExampleHolograms implements Listener {

    private final Plugin plugin;
    private final HologramManager hologramManager;
    private final Map<String, String> hologramIds = new HashMap<>(); // Maps custom names to hologram IDs
    private int hologramCounter = 0; // Counter for generating unique IDs

    /**
     * @param plugin The plugin which uses the lib
     */
    public ExampleHolograms(Plugin plugin) {
        this.plugin = plugin;
        this.hologramManager = new HologramManager(plugin);
    }


    /**
     * Deactivates all holograms managed by this instance.
     */
    public void deactivateHolograms() {
        for (String hologramId : hologramIds.values()) {
            hologramManager.removeHologram(hologramId);
        }
        hologramIds.clear();
    }

    /**
     * Deactivates a specific hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     */
    public void deactivateHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            hologramManager.removeHologram(hologramId);
            hologramIds.remove(customName);
        }
    }

    /**
     * Deactivates a specific hologram by its Hologram object.
     * @param hologram The hologram to deactivate
     */
    public void deactivateHologram(Hologram hologram) {
        if (hologram != null) {
            hologramManager.removeHologram(hologram.getId());
            // Remove from our tracking map
            hologramIds.entrySet().removeIf(entry -> entry.getValue().equals(hologram.getId()));
        }
    }

    /**
     * Creates a hologram at the specified location with the given text.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     */
    public void appendHOLO(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        try {
            Hologram hologram = hologramManager.createHologram(hologramId, location, text, 2.0, true);
            if (hologram != null) {
                hologramIds.put(customName, hologramId);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create hologram: " + e.getMessage());
        }
    }

    /**
     * Creates a hologram at the specified location with the given text and returns the hologram object.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     * @return The created hologram, or null if creation failed
     */
    public Hologram appendHOLO2(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        try {
            Hologram hologram = hologramManager.createHologram(hologramId, location, text, 2.0, true);
            if (hologram != null) {
                hologramIds.put(customName, hologramId);
                return hologram;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create hologram: " + e.getMessage());
        }
        return null;
    }
    /**
     * Gets the hologram manager instance for advanced operations.
     * @return The HologramManager instance
     */
    public HologramManager getHologramManager() {
        return hologramManager;
    }

    /**
     * Gets a hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     * @return The hologram, or null if not found
     */
    public Hologram getHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.getHologram(hologramId);
        }
        return null;
    }

    /**
     * Updates the text of a hologram by its custom name.
     * @param customName The custom name of the hologram
     * @param newText The new text to display
     * @return true if the text was updated successfully, false otherwise
     */
    public boolean updateHologramText(String customName, String newText) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.updateHologramText(hologramId, newText);
        }
        return false;
    }

    /**
     * Cleanup method to be called when the plugin is disabled.
     * Removes all holograms and shuts down the hologram manager.
     */
    public void cleanup() {
        deactivateHolograms();
        hologramManager.shutdown();
    }

    // Note: The old hologram library events (PlayerHologramShowEvent, PlayerHologramHideEvent, PlayerHologramInteractEvent)
    // are not available in the new hologram system. If you need similar functionality, you would need to implement
    // custom event handling using the new hologram system's capabilities or create custom events.
}