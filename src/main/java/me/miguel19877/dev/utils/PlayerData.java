package me.miguel19877.dev.utils;

import java.util.UUID;

public class PlayerData {

    private final UUID uuid;
    private String username;
    private long coins;
    private long money;
    private long almas;
    private int rankId;
    private long blocksMined;
    private long kills;
    private long deaths;
    private int prestige;
    private String twitchChannel;
    private int groupId;
    private long vipExpiration;
    private String language;

    public PlayerData(UUID uuid, String username, long coins, long money, long almas, int rankId, long blocksMined, long kills, long deaths, int prestige, String twitchChannel, int groupId, long vipExpiration, String language) {
        this.uuid = uuid;
        this.username = username;
        this.coins = coins;
        this.money = money;
        this.almas = almas;
        this.rankId = rankId;
        this.blocksMined = blocksMined;
        this.kills = kills;
        this.deaths = deaths;
        this.prestige = prestige;
        this.twitchChannel = twitchChannel;
        this.groupId = groupId;
        this.vipExpiration = vipExpiration;
        this.language = language;
    }

    // --- <PERSON><PERSON> and Set<PERSON> ---

    public UUID getUuid() {
        return uuid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public long getCoins() {
        return coins;
    }

    public void setCoins(long coins) {
        this.coins = coins;
    }

    public long getMoney() {
        return money;
    }

    public void setMoney(long money) {
        this.money = money;
    }

    public long getAlmas() {
        return almas;
    }

    public void setAlmas(long almas) {
        this.almas = almas;
    }

    public int getRankId() {
        return rankId;
    }

    public void setRankId(int rankId) {
        this.rankId = rankId;
    }

    public long getBlocksMined() {
        return blocksMined;
    }

    public void setBlocksMined(long blocksMined) {
        this.blocksMined = blocksMined;
    }

    public long getKills() {
        return kills;
    }

    public void setKills(long kills) {
        this.kills = kills;
    }

    public long getDeaths() {
        return deaths;
    }

    public void setDeaths(long deaths) {
        this.deaths = deaths;
    }

    public int getPrestige() {
        return prestige;
    }

    public void setPrestige(int prestige) {
        this.prestige = prestige;
    }

    public String getTwitchChannel() {
        return twitchChannel;
    }

    public void setTwitchChannel(String twitchChannel) {
        this.twitchChannel = twitchChannel;
    }

    public int getgroupId() {
        return groupId;
    }

    public void setgroupId(int groupId) {
        this.groupId = groupId;
    }

    public long getVipExpiration() {
        return vipExpiration;
    }

    public void setVipExpiration(long vipExpiration) {
        this.vipExpiration = vipExpiration;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

}
