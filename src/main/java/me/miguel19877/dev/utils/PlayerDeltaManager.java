package me.miguel19877.dev.utils;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class PlayerDeltaManager {

    // The map that holds pending changes before they are batched and sent.
    private final ConcurrentHashMap<UUID, PlayerDataDelta> deltaMap = new ConcurrentHashMap<>();

    // Internal helper to get or create a delta object for a player
    private PlayerDataDelta getOrCreateDelta(UUID uuid) {
        return deltaMap.computeIfAbsent(uuid, PlayerDataDelta::new);
    }


    public void recordMoneyChange(UUID uuid, long newMoneyValue) {
        getOrCreateDelta(uuid).setMoney(newMoneyValue);
    }

    public void recordCoinsChange(UUID uuid, long newCoinsValue) {
        getOrCreateDelta(uuid).setCoins(newCoinsValue);
    }

    public void recordPrestigeChange(UUID uuid, int newPrestigeValue) {
        getOrCreateDelta(uuid).setPrestige(newPrestigeValue);
    }

    public void recordKillsChange(UUID uuid, long newKillsValue) {
        getOrCreateDelta(uuid).setKills(newKillsValue);
    }

    public void recordDeathsChange(UUID uuid, long newDeathsValue) {
        getOrCreateDelta(uuid).setDeaths(newDeathsValue);
    }

    public void recordRankidChange(UUID uuid, int rankId) {
        getOrCreateDelta(uuid).setRankId(rankId);
    }

    public void recordBlocksminedChange(UUID uuid, long blocksMined) {
        getOrCreateDelta(uuid).setBlocksMined(blocksMined);
    }

    public void recordAlmasChange(UUID uuid, long almas) {
        getOrCreateDelta(uuid).setAlmas(almas);
    }

    public void recordTwitchChannelChange(UUID uuid, String twitchChannel) {
        getOrCreateDelta(uuid).setTwitchChannel(twitchChannel);
    }

    public void recordGroupIdChange(UUID uuid, Integer groupId) {
        getOrCreateDelta(uuid).setGroupId(groupId);
    }

    public void recordVipExpirationChange(UUID uuid, Long vipExpiration) {
        getOrCreateDelta(uuid).setVipExpiration(vipExpiration);
    }

    public void recordLanguageChange(UUID uuid, String language) {
        getOrCreateDelta(uuid).setLanguage(language);
    }

    public void recordAchievementProgress(UUID uuid, String achievementId, int progress) {
        // This method can be used if we want to batch achievement updates
        // For now, achievements are sent immediately via AchievementManager
        // But we keep this for potential future use
    }


    /**
     * This method would be called by your scheduler every second.
     * It clears the map and returns the deltas to be processed/sent.
     */
    public ConcurrentHashMap<UUID, PlayerDataDelta> getAndClearDeltas() {
        // In a real implementation, you would need to properly handle concurrency here.
        // This is a simplified example. A common pattern is to swap the map.
        ConcurrentHashMap<UUID, PlayerDataDelta> oldMap = new ConcurrentHashMap<>(deltaMap);
        deltaMap.clear();
        return oldMap;
    }
}