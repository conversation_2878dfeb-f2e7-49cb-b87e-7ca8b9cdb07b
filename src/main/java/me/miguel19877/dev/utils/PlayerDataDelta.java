package me.miguel19877.dev.utils;

import java.util.UUID;

public class PlayerDataDelta {
    private UUID uuid;
    private Long coins;
    private Long money;
    private Long almas;
    private Integer rankId;
    private Long blocksMined;
    private Long kills;
    private Long deaths;
    private Integer prestige;
    private String twitchChannel;
    private Integer groupId;
    private Long vipExpiration;
    private String language;

    public PlayerDataDelta(UUID uuid) {this.uuid = uuid;}

    public UUID getUuid() { return uuid; }
    public void setUuid(UUID uuid) { this.uuid = uuid; }

    public Long getCoins() { return coins; }
    public void setCoins(Long coins) { this.coins = coins; }

    public Long getMoney() { return money; }
    public void setMoney(Long money) { this.money = money; }

    public Long getAlmas() { return almas; }
    public void setAlmas(Long almas) { this.almas = almas; }

    public Long getBlocksMined() { return blocksMined; }
    public void setBlocksMined(Long blocksMined) { this.blocksMined = blocksMined; }

    public Long getKills() { return kills; }
    public void setKills(Long kills) { this.kills = kills; }

    public Long getDeaths() { return deaths; }
    public void setDeaths(Long deaths) { this.deaths = deaths; }

    public Integer getRankId() { return rankId; }
    public void setRankId(Integer rankId) { this.rankId = rankId; }

    public Integer getPrestige() { return prestige; }
    public void setPrestige(Integer prestige) { this.prestige = prestige; }

    public String getTwitchChannel() { return twitchChannel;}
    public void setTwitchChannel(String twitchChannel) { this.twitchChannel = twitchChannel; }

    public Integer getGroupId() { return groupId; }
    public void setGroupId(Integer groupId) { this.groupId = groupId; }

    public Long getVipExpiration() { return vipExpiration;}
    public void setVipExpiration(Long vipExpiration) { this.vipExpiration = vipExpiration; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }
}