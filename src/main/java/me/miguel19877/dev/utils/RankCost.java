package me.miguel19877.dev.utils;

public enum RankCost {

    RANK_1(1, 30000),
    RANK_2(2, 65000),
    RANK_3(3, 150000),
    RANK_4(4, 400000),
    RANK_5(5, 750000),
    RANK_6(6, 1000000),
    RANK_7(7, 2500000),
    RANK_8(8, 5000000),
    RANK_9(9, 8500000),
    RANK_10(10, 25000000),
    RANK_11(11, 60000000),
    RANK_12(12, 200000000),
    RANK_13(13, 650000000),
    RANK_14(14, 1000000000),
    RANK_15(15, 3000000000L),
    RANK_16(16, 6500000000L),
    RANK_17(17, 12000000000L),
    RANK_18(18, 30000000000L),
    RANK_19(19, 65000000000L),
    RANK_20(20, 100000000000L),
    RANK_21(21, 450000000000L);

    private final int rankId;
    private final long cost;

    RankCost(int rankId, long cost) {
        this.rankId = rankId;
        this.cost = cost;
    }

    public int getRankId() {
        return rankId;
    }

    public long getCost() {
        return cost;
    }

    public static long getCostByRankId(int rankId) {
        for (RankCost rankCost : values()) {
            if (rankCost.getRankId() == rankId) {
                return rankCost.getCost();
            }
        }
        return 0;
    }
}
