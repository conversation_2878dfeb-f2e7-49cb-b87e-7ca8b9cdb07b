package me.miguel19877.dev;

import me.miguel19877.dev.managers.RedisManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import redis.clients.jedis.Jedis;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.utils.InventorySerializer;  // Assuming you have this utility

import java.util.UUID;

public class KitManager {

    private static final String KIT_KEY_RANK = "kit:rank:";
    private static final String KIT_KEY_VIP = "kit:vip:";
    private static final String COOLDOWN_KEY = "cooldown:";
    private static final String VIP_COOLDOWN_KEY = "vip_cooldown:";


    // Utility to get a Jedis connection from the Redis manager
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    // Set a kit for a specific rank or VIP level
    public static void setKit(Player admin, int level, String type, int cooldownSeconds) {
        try (Jedis jedis = getJedis()) {
            ItemStack[] items = admin.getInventory().getContents();
            //Remove AIR items
            for (int i = 0; i < items.length; i++) {
                if (items[i] != null && items[i].getType() == Material.AIR) {
                    items[i] = null;
                }
            }
            String serializedInventory = InventorySerializer.serialize(items);
            String key = (type.equalsIgnoreCase("rank") ? KIT_KEY_RANK : KIT_KEY_VIP) + level;
            jedis.set(key, serializedInventory);
            if (cooldownSeconds > 0) {
                jedis.set(COOLDOWN_KEY + key, String.valueOf( cooldownSeconds * 1000L));
            }
            Bukkit.getLogger().info("Kit set for " + type + " level " + level + " by " + admin.getName());
        }
    }

    // Retrieve a kit for a specific player based on their rank or VIP status
    public static ItemStack[] getKit(Player player, String type) {
        int level = (type.equalsIgnoreCase("rank") ? RankSystem.getRankId(player) : Permissions.getGrupoId(player));
        String key = (type.equalsIgnoreCase("rank") ? KIT_KEY_RANK : KIT_KEY_VIP) + level;

        try (Jedis jedis = getJedis()) {
            String serializedInventory = jedis.get(key);
            if (serializedInventory != null) {
                long cooldownSeconds = Long.parseLong(jedis.get(COOLDOWN_KEY + key));
                resetCooldown(player, cooldownSeconds);
                return InventorySerializer.deserialize(serializedInventory);
            }
        }
        return new ItemStack[0]; // Return an empty inventory if no kit is set
    }

    // Check if the cooldown for receiving a new kit has expired
    public static boolean isCooldownExpired(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String cooldown = jedis.get(COOLDOWN_KEY + playerId);
            return cooldown == null || System.currentTimeMillis() > Long.parseLong(cooldown);
        }
    }

    // Reset the cooldown for a player
    public static void resetCooldown(Player player, long cooldownMillis) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.set(COOLDOWN_KEY + playerId, String.valueOf(System.currentTimeMillis() + cooldownMillis));
        }
    }

    public static Long getCooldown(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String cooldown = jedis.get(COOLDOWN_KEY + playerId);
            return cooldown == null ? 0 : Long.parseLong(cooldown);
        }
    }

    public static void resetVIPCooldown(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.set(VIP_COOLDOWN_KEY + playerId, String.valueOf(System.currentTimeMillis()));
        }
    }

    public static boolean isVIPCooldownExpired(Player player) {

        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String cooldown = jedis.get(VIP_COOLDOWN_KEY + playerId);
            return cooldown == null || System.currentTimeMillis() > Long.parseLong(cooldown);
        }

    }

    public static void setVIPCooldown(Player player, long cooldownMillis) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.set(VIP_COOLDOWN_KEY + playerId, String.valueOf(System.currentTimeMillis() + cooldownMillis));
        }
    }

    public static Long getVIPCooldown(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String cooldown = jedis.get(VIP_COOLDOWN_KEY + playerId);
            return cooldown == null ? 0 : Long.parseLong(cooldown);
        }
    }

}
