package me.miguel19877.dev;

import me.miguel19877.dev.managers.RedisManager;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;

import java.util.*;

public class RankSystem {

    private static final HashMap<UUID, Integer> ranks = new HashMap<>();
    private static final String RANK_KEY = "rank:";

    // Utility method to get a Jedis connection
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }


    public static int getRankId(Player player) {
        return ranks.getOrDefault(player.getUniqueId(), 1);  // Default rank id 1
    }

    public static String getRankName(int rankId) {
        String group = "Membro";
        switch (rankId) {
            case 1:
                group = "[Novato]";
                break;
            case 2:
                group = "[Iniciante]";
                break;
            case 3:
                group = "[Estudante]";
                break;
            case 4:
                group = "[Aprendiz]";
                break;
            case 5:
                group = "[Estagiário]";
                break;
            case 6:
                group = "[Empregado]";
                break;
            case 7:
                group = "[Promovido]";
                break;
            case 8:
                group = "[Revisor]";
                break;
            case 9:
                group = "[Sub-Gerente]";
                break;
            case 10:
                group = "[Gerente]";
                break;
            case 11:
                group = "[Sub-Chefe]";
                break;
            case 12:
                group = "[Chefe]";
                break;
            case 13:
                group = "[Presidente]";
                break;
            case 14:
                group = "[Milionário]";
                break;
            case 15:
                group = "[Bilionário]";
                break;
            case 16:
                group = "[Rei]";
                break;
            case 17:
                group = "[Divino]";
                break;
            case 18:
                group = "[Semi Deus]";
                break;
            case 19:
                group = "[Omniciente]";
                break;
            case 20:
                group = "[Omnipotente]";
                break;
            case 21:
                group = "[Omnipresente]";
                break;
            case 22:
                group = "[Deus]";
                break;
            default:
                break;
        }
        return group;
    }

    public static void setRank(Player player, int rankId) {
        ranks.put(player.getUniqueId(), rankId);
        // Optionally save to Redis immediately
        savePlayer(player);
        Rankup.getInstance().deltaManager.recordRankidChange(player.getUniqueId(), rankId);
    }

    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String rankId = jedis.get(RANK_KEY + playerId);
            ranks.put(playerId, rankId != null ? Integer.parseInt(rankId) : 1);  // Default rank id 1 if not set
        }
    }

    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        Integer rankId = ranks.get(playerId);
        if (rankId != null) {
            try (Jedis jedis = getJedis()) {
                jedis.set(RANK_KEY + playerId, String.valueOf(rankId));
            }
        }
    }
}
