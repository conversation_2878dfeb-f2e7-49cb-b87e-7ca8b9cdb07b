package me.miguel19877.dev.commands;

import me.miguel19877.dev.<PERSON>up;
import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;

public class Warps implements CommandExecutor, Listener {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("warp") || command.getName().equalsIgnoreCase("warps")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                p.openInventory(Rankup.warpsInventory);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("encantamentos")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                LanguageManager.getInstance().sendMessage(p, "general.under_construction");
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("loja")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                loja(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("spawn") || command.getName().equalsIgnoreCase("lobby")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                spawn(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("minas")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                minas(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        if (command.getName().equalsIgnoreCase("minapvp")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                minapvp(p);
            }else {
                sender.sendMessage("§c§lThis command can only be used by players!");
            }
            return true;
        }
        return false;
    }

    private void encantamentos(Player p) {
        p.teleport(new Location(Bukkit.getWorld("mundo"), 633.5, 4.5, 51.5));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_enchantments");
    }

    private void minapvp(Player p) {
        p.teleport(new Location(Bukkit.getWorld("minas"), 213.5, 41.3, -133.5, -90, 0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_minapvp");
    }

    private void loja(Player p) {
        p.teleport(new Location(Bukkit.getWorld("loja"), 261.5, 30.5, 196.5, 45, 0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_shop");
    }

    private void spawn(Player p) {
        p.teleport(new Location(Bukkit.getWorld("rankupspawn"), 818.5, 5.5, -83.5, 0, 0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_spawn");
    }

    private void minas(Player p) {
        p.teleport(new Location(Bukkit.getWorld("rankupspawn"), 614.5, 6.5, -71.5));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_mines");
    }

    private void plots(Player p) {
        p.teleport(new Location(Bukkit.getWorld("plotworld"), 0.0, 66.5, 0.0));
        LanguageManager.getInstance().sendMessage(p, "warps.teleported_plots");
    }

    @EventHandler
    public void clickWarpsInventory(InventoryClickEvent e) {
        if (e.getInventory().equals(Rankup.warpsInventory)) {
            e.setCancelled(true);
            if(e.getCurrentItem() == null) return;
            if (e.getCurrentItem() != null && e.getCurrentItem().hasItemMeta() && e.getCurrentItem().getItemMeta().hasDisplayName()) {
                Player p = (Player) e.getWhoClicked();
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lEncantamentos")) {
                    LanguageManager.getInstance().sendMessage(p, "general.under_construction");
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lCaixa")) {
                    LanguageManager.getInstance().sendMessage(p, "warps.crate_development");
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lLoja")) {
                    loja(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lSpawn")) {
                    spawn(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lMinas")) {
                    minas(p);
                }
                if (e.getCurrentItem().getItemMeta().getDisplayName().equals("§6§lPlots")) {
                    plots(p);
                }
            }
            e.getWhoClicked().closeInventory();
        }
    }
}
