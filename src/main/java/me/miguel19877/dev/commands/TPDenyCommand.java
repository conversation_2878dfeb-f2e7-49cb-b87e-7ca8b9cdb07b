package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.TPAManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class TPDenyCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return true;
        }
        
        Player player = (Player) sender;
        TPAManager tpaManager = TPAManager.getInstance();
        
        // Check if player has a pending request
        if (!tpaManager.hasPendingRequest(player)) {
            String noPendingMessage = LanguageManager.getInstance().getMessage(player, "tpa.no_pending_request");
            if (noPendingMessage == null) {
                noPendingMessage = "§cNão tens nenhum pedido de teleporte pendente!";
            }
            player.sendMessage(noPendingMessage);
            return true;
        }
        
        // Deny the request
        boolean success = tpaManager.denyTPARequest(player);
        
        if (!success) {
            String errorMessage = LanguageManager.getInstance().getMessage(player, "tpa.deny_failed");
            if (errorMessage == null) {
                errorMessage = "§cFalha ao recusar o pedido de teleporte.";
            }
            player.sendMessage(errorMessage);
        }
        
        return true;
    }
}
