package me.miguel19877.dev.commands;

import me.miguel19877.dev.Tasks.Minas;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class MineReset implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cOnly players can execute this command.");
            return true;
        }

        Player player = (Player) sender;
        
        // Check if player has admin permissions
        if (Permissions.getGrupoId(player) < 12) {
            LanguageManager.getInstance().sendMessage(player, "general.no_permission");
            return true;
        }

        if (args.length == 0) {
            player.sendMessage("§c§lMine Reset Commands:");
            player.sendMessage("§7/minereset force - Force mine reset");
            player.sendMessage("§7/minereset status - Check system status");
            player.sendMessage("§7/minereset info - Show all mine compositions");
            player.sendMessage("§7/minereset composition <mine> - Show specific mine composition");
            player.sendMessage("§7/minereset physics <add|remove> <material> - Manage physics materials");
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "force":
                player.sendMessage("§aForcing mine reset...");
                Minas.forceReset();
                return true;
                
            case "status":
                player.sendMessage("§a§lMine Reset System Status:");
                player.sendMessage("§7Queue status: §f" + Minas.getQueueStatus());
                player.sendMessage("§7Registered mines: §f" + Minas.getMineNames().size());
                player.sendMessage("§7Available compositions: §f" + Minas.getCompositionNames().size());
                player.sendMessage("§7Physics materials: §f" + getPhysicsMaterialsList());
                return true;
                
            case "info":
                player.sendMessage(Minas.getMineInfo());
                return true;
                
            case "composition":
                if (args.length < 2) {
                    player.sendMessage("§cUsage: /minereset composition <mine_name>");
                    player.sendMessage("§7Available mines: §f" + String.join(", ", Minas.getMineNames()));
                    return true;
                }
                
                String mineName = args[1];
                Minas.MineComposition composition = Minas.getMineComposition(mineName);
                
                if (composition == null) {
                    player.sendMessage("§cMine composition not found: " + mineName);
                    player.sendMessage("§7Available mines: §f" + String.join(", ", Minas.getMineNames()));
                    return true;
                }
                
                player.sendMessage("§a§lComposition for mine: §f" + mineName);
                for (Minas.WeightedBlock block : composition.getBlocks()) {
                    double percentage = block.getPercentage(composition.getTotalWeight());
                    player.sendMessage("§7- §f" + block.material.name() + " §7(" + String.format("%.1f", percentage) + "%)");
                }
                return true;
                
            case "physics":
                if (args.length < 3) {
                    player.sendMessage("§cUsage: /minereset physics <add|remove> <material>");
                    return true;
                }
                
                String action = args[1].toLowerCase();
                String materialName = args[2].toUpperCase();
                
                try {
                    Material material = Material.valueOf(materialName);
                    
                    if (action.equals("add")) {
                        Minas.addPhysicsMaterial(material);
                        player.sendMessage("§aAdded " + material.name() + " to physics materials.");
                    } else if (action.equals("remove")) {
                        Minas.removePhysicsMaterial(material);
                        player.sendMessage("§aRemoved " + material.name() + " from physics materials.");
                    } else {
                        player.sendMessage("§cInvalid action. Use 'add' or 'remove'.");
                    }
                } catch (IllegalArgumentException e) {
                    player.sendMessage("§cInvalid material: " + materialName);
                }
                return true;
                
            default:
                player.sendMessage("§cUnknown subcommand. Use /minereset for help.");
                return true;
        }
    }
    
    private String getPhysicsMaterialsList() {
        // We can't directly access the PHYSICS_MATERIALS set, so we'll list the known ones
        String[] knownPhysicsMaterials = {"SAND", "GRAVEL", "ANVIL", "DRAGON_EGG", "CONCRETE_POWDER"};
        return String.join(", ", knownPhysicsMaterials);
    }
}
