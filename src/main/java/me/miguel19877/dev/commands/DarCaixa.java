package me.miguel19877.dev.commands;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;

public class DarCaixa implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("darcaixa")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                if (Permissions.getGrupoId(p) >=13) {
                    if (args.length == 0 || args.length == 1 || args.length > 2) {
                        LanguageManager.getInstance().sendMessage(p, "darcaixa.usage");
                        return true;
                    }
                    if (args.length == 2) {
                        Player target = p.getServer().getPlayer(args[0]);
                        ItemStack key = new ItemStack(Material.TRIPWIRE_HOOK, 1);
                        ItemMeta keymeta = key.getItemMeta();
                        keymeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                        keymeta.setDisplayName(LanguageManager.getInstance().getMessage(p, "darcaixa.box_name", args[1]));
                        keymeta.setLore(Arrays.asList(LanguageManager.getInstance().getMessage(p, "darcaixa.box_lore", args[1].toLowerCase())));
                        key.setItemMeta(keymeta);

                        key.addUnsafeEnchantment(Enchantment.DURABILITY, 1);
                        if (target != null) {
                            target.getInventory().addItem(key);
                            target.updateInventory();
                            LanguageManager.getInstance().sendMessage(p, "darcaixa.success", args[1], target.getName());
                        } else {
                            LanguageManager.getInstance().sendMessage(p, "general.player_not_found");
                        }
                        return true;

                    }
                } else {
                    LanguageManager.getInstance().sendMessage(p, "darcaixa.no_permission");
                    return true;
                }

            }else {
                Player target = Rankup.getInstance().getServer().getPlayer(args[0]);
                ItemStack key = new ItemStack(Material.CHEST, 1);
                ItemMeta keymeta = key.getItemMeta();
                keymeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                keymeta.setDisplayName("§a§lCaixa " + args[1]);
                keymeta.setLore(Arrays.asList("§7Caixa " + args[1].toLowerCase()));
                key.setItemMeta(keymeta);

                key.addUnsafeEnchantment(Enchantment.DURABILITY, 1);
                if (target != null) {
                    target.getInventory().addItem(key);
                    target.updateInventory();
                    sender.sendMessage("§a§lYou gave a " + args[1] + " key to " + target.getName());
                } else {
                    sender.sendMessage("§c§lPlayer not found!");
                }
            }
        }
        return true;
    }
}
