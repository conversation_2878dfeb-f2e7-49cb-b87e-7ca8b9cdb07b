package me.miguel19877.dev.commands;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.utils.RankCost;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Rank implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("rank")) {
            Player player = (Player) commandSender;
            LanguageManager langManager = LanguageManager.getInstance();

            int rankId = RankSystem.getRankId(player);
            String rank = RankSystem.getRankName(rankId);
            langManager.sendMessage(player, "rank.current", rank);

            long nextCustom = RankCost.getCostByRankId(rankId);
            long balanceatual = Economy.getMoney(player.getUniqueId());
            long falta = nextCustom - balanceatual;

            if (rankId == 22) {
                langManager.sendMessage(player, "rank.max_rank");
            } else {
                if (falta > 0) {
                    langManager.sendMessage(player, "rank.need_money", String.valueOf(falta));
                } else {
                    langManager.sendMessage(player, "rank.enough_money");
                }
            }
        }
        return false;
    }
}
