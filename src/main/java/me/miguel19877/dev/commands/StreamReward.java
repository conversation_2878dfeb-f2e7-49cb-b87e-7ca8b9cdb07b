package me.miguel19877.dev.commands;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.Tasks.LivestreamTrack;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.StreamRewardManager;
import me.miguel19877.dev.managers.TwitchManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;

public class StreamReward implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cOnly players can execute this command.");
            return true;
        }

        Player player = (Player) sender;
        
        // Check if player has admin permissions
        if (Permissions.getGrupoId(player) < 12) {
            LanguageManager.getInstance().sendMessage(player, "general.no_permission");
            return true;
        }

        if (args.length == 0) {
            LanguageManager.getInstance().sendMessage(player, "streamreward.usage");
            LanguageManager.getInstance().sendMessage(player, "streamreward.usage_check");
            LanguageManager.getInstance().sendMessage(player, "streamreward.usage_reset");
            LanguageManager.getInstance().sendMessage(player, "streamreward.usage_info");
            player.sendMessage("§7/streamreward debug - Debug current system state");
            player.sendMessage("§7/streamreward trigger - Manually trigger livestream check");
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "check":
                if (args.length != 3) {
                    LanguageManager.getInstance().sendMessage(player, "streamreward.check_usage");
                    return true;
                }

                Player targetPlayer = Bukkit.getPlayer(args[1]);
                if (targetPlayer == null) {
                    LanguageManager.getInstance().sendMessage(player, "streamreward.player_not_found");
                    return true;
                }

                String streamerChannel = args[2];
                long watchTime = StreamRewardManager.getWatchTime(targetPlayer.getUniqueId(), streamerChannel);

                LanguageManager.getInstance().sendMessage(player, "streamreward.watch_time_info");
                LanguageManager.getInstance().sendMessage(player, "streamreward.player_label", targetPlayer.getName());
                LanguageManager.getInstance().sendMessage(player, "streamreward.streamer_label", streamerChannel);
                LanguageManager.getInstance().sendMessage(player, "streamreward.watch_time_label", formatTime(player, watchTime));
                LanguageManager.getInstance().sendMessage(player, "streamreward.rewards_status");

                String reward30Status = watchTime >= 1800 ?
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_eligible") :
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_not_reached");
                String reward1hStatus = watchTime >= 3600 ?
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_eligible") :
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_not_reached");
                String reward2hStatus = watchTime >= 7200 ?
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_eligible") :
                    LanguageManager.getInstance().getMessage(player, "streamreward.reward_not_reached");

                LanguageManager.getInstance().sendMessage(player, "streamreward.reward_30min", reward30Status);
                LanguageManager.getInstance().sendMessage(player, "streamreward.reward_1hour", reward1hStatus);
                LanguageManager.getInstance().sendMessage(player, "streamreward.reward_2hours", reward2hStatus);
                return true;
                
            case "reset":
                if (args.length != 3) {
                    LanguageManager.getInstance().sendMessage(player, "streamreward.reset_usage");
                    return true;
                }

                Player resetPlayer = Bukkit.getPlayer(args[1]);
                if (resetPlayer == null) {
                    // Try to get UUID from offline player
                    UUID offlineId = UUID.nameUUIDFromBytes(("OfflinePlayer:" + args[1]).getBytes());
                    StreamRewardManager.resetPlayerRewards(offlineId, args[2]);
                    LanguageManager.getInstance().sendMessage(player, "streamreward.reset_success_offline", args[1], args[2]);
                } else {
                    StreamRewardManager.resetPlayerRewards(resetPlayer.getUniqueId(), args[2]);
                    LanguageManager.getInstance().sendMessage(player, "streamreward.reset_success_online", resetPlayer.getName(), args[2]);
                }
                return true;
                
            case "info":
                LanguageManager.getInstance().sendMessage(player, "streamreward.system_info");
                LanguageManager.getInstance().sendMessage(player, "streamreward.live_streamers", String.valueOf(TwitchManager.getChannelLive().size()));
                for (String liveStreamer : TwitchManager.getChannelLive()) {
                    LanguageManager.getInstance().sendMessage(player, "streamreward.streamer_list", liveStreamer);
                }
                LanguageManager.getInstance().sendMessage(player, "streamreward.reward_thresholds");
                LanguageManager.getInstance().sendMessage(player, "streamreward.threshold_30min");
                LanguageManager.getInstance().sendMessage(player, "streamreward.threshold_1hour");
                LanguageManager.getInstance().sendMessage(player, "streamreward.threshold_2hours");
                return true;

            case "debug":
                player.sendMessage("§a§lStream System Debug Info:");
                player.sendMessage("§7Online players with Twitch accounts:");
                for (Player p : Bukkit.getOnlinePlayers()) {
                    if (TwitchManager.checkPlayerTwitch(p.getUniqueId())) {
                        String twitchName = TwitchManager.getTwitchChannel(p.getUniqueId());
                        player.sendMessage("  §f" + p.getName() + " §7-> §f" + twitchName);
                    }
                }
                player.sendMessage("§7Live streamers: §f" + TwitchManager.getChannelLive().toString());
                return true;

            case "trigger":
                player.sendMessage("§aManually triggering livestream check...");
                Bukkit.getScheduler().runTaskAsynchronously(Rankup.getInstance(), new LivestreamTrack());
                player.sendMessage("§aLivestream check triggered! Check console for debug output.");
                return true;

            default:
                LanguageManager.getInstance().sendMessage(player, "streamreward.unknown_subcommand");
                return true;
        }
    }
    
    private String formatTime(Player player, long seconds) {
        if (seconds < 60) {
            return LanguageManager.getInstance().getMessage(player, "streamreward.time_seconds", String.valueOf(seconds));
        } else if (seconds < 3600) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return LanguageManager.getInstance().getMessage(player, "streamreward.time_minutes",
                String.valueOf(minutes), String.valueOf(remainingSeconds));
        } else {
            long hours = seconds / 3600;
            long remainingMinutes = (seconds % 3600) / 60;
            long remainingSeconds = seconds % 60;
            return LanguageManager.getInstance().getMessage(player, "streamreward.time_hours",
                String.valueOf(hours), String.valueOf(remainingMinutes), String.valueOf(remainingSeconds));
        }
    }
}
