package me.miguel19877.dev.commands;

import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class BroadcastCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        // Check permissions
        if (sender instanceof Player) {
            Player player = (Player) sender;
            if (Permissions.getGrupoId(player) < 13) {
                player.sendMessage("§cNão tens permissão para usar este comando!");
                return true;
            }
        }
        
        if (args.length == 0) {
            sender.sendMessage("§cUso: /broadcast <mensagem>");
            sender.sendMessage("§7Use & para cores. Exemplo: /broadcast &aOlá &bmundo!");
            return true;
        }
        
        // Join all arguments into a single message
        StringBuilder messageBuilder = new StringBuilder();
        for (int i = 0; i < args.length; i++) {
            messageBuilder.append(args[i]);
            if (i < args.length - 1) {
                messageBuilder.append(" ");
            }
        }
        
        String message = messageBuilder.toString();
        
        // Replace color codes
        message = ChatColor.translateAlternateColorCodes('&', message);
        
        // Format the broadcast message with prefix
        String broadcastMessage = "§6§l[ANÚNCIO] §r" + message;
        
        // Broadcast to all online players
        Bukkit.broadcastMessage("");
        Bukkit.broadcastMessage(broadcastMessage);
        Bukkit.broadcastMessage("");

        
        return true;
    }
}
