package me.miguel19877.dev.commands;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.<PERSON><PERSON>anager;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.listeners.AchievementRankListener;
import me.miguel19877.dev.utils.RankCost;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Rankup implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("rankup")) {
            if (sender instanceof Player) {
                if (args.length == 0) {
                    Player p = (Player) sender;
                    LanguageManager langManager = LanguageManager.getInstance();

                    int rankid = RankSystem.getRankId(p);
                    long custo = RankCost.getCostByRankId(rankid);

                    if (rankid == 22) {
                        langManager.sendMessage(p, "rankup.max_rank");
                    } else {
                        if (Economy.getMoney(p.getUniqueId()) >= custo) {
                            Economy.removeMoney(p.getUniqueId(), (long) custo);
                            RankSystem.setRank(p, rankid + 1);
                            KitManager.resetCooldown(p, 0);
                            langManager.sendMessage(p, "rankup.success");

                            // Check rank achievements
                            AchievementRankListener.checkRankAchievements(p);
                        } else {
                            long falta = custo - Economy.getMoney(p.getUniqueId());
                            langManager.sendMessage(p, "rankup.insufficient_funds", String.valueOf(falta));
                        }
                    }
                }
            } else {
                sender.sendMessage("This command can only be executed by a player.");
            }
        }
        return true;
    }
}
