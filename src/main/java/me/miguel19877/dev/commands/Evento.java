package me.miguel19877.dev.commands;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.TitleAPI;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;

public class Evento implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("evento")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                if (Permissions.getGrupoId(p) >=13) {
                    if (args.length == 0 || args.length > 1) {
                        sender.sendMessage("§c§lUse /evento comecar");
                        return true;
                    }
                    if (args.length == 1) {
                        if (args[0].equalsIgnoreCase("comecar")) {
                            Bukkit.getScheduler().runTaskTimerAsynchronously(Rankup.getInstance(), new Runnable() {
                                int seconds = 11;
                                @Override
                                public void run() {
                                    seconds--;

                                    if (seconds > 1) {
                                        Bukkit.broadcastMessage("§cO evento começará em §a" + seconds + " segundos.");
                                        for (Player p2 : Bukkit.getOnlinePlayers()) {
                                            TitleAPI.sendTitle(p2, 5, 10, 5, "§a§l" + seconds, "");
                                        }
                                    }
                                    if (seconds == 1) {
                                        Bukkit.broadcastMessage("§cO evento começará em §a" + seconds + " segundo.");
                                        for (Player p2 : Bukkit.getOnlinePlayers()) {
                                            TitleAPI.sendTitle(p2, 5, 10, 5, "§a§l" + seconds, "");
                                        }
                                    }
                                    if (seconds == 0) {
                                        Bukkit.broadcastMessage("§a§lEvento iniciado! §cBoa sorte a todos");
                                        TitleAPI.sendTitle(p, 10, 80, 10, "§6§lEVENTO", "§c§lO Evento começou!");
                                        me.miguel19877.dev.Tasks.Evento.setSeconds();
                                        me.miguel19877.dev.Tasks.Evento.start(0,20);
                                    }
                                }
                            }, 0, 20);
                        }
                        return true;

                    }
                }else {
                    sender.sendMessage("§cApenas jogadores com o grupo §6[DEV] §cou superiores podem usar este comando.");
                    return true;
                }

            }else {
                Player target = Rankup.getInstance().getServer().getPlayer(args[0]);
                ItemStack key = new ItemStack(Material.CHEST, 1);
                ItemMeta keymeta = key.getItemMeta();
                keymeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                keymeta.setDisplayName("§a§lCaixa " + args[1]);
                keymeta.setLore(Arrays.asList("§7Caixa " + args[1].toLowerCase()));
                key.setItemMeta(keymeta);

                switch (args[1].toLowerCase()) {
                    case "lapis":
                        key.addUnsafeEnchantment(Enchantment.DURABILITY, 1);
                        break;
                    default:
                        sender.sendMessage("§c§lUse /darcaixa <jogador> <tipo>");
                        return true;
                }
                if (target != null) {
                    target.getInventory().addItem(key);
                    sender.sendMessage("§a§lVocê deu uma chave " + args[1] + " para " + target.getName());
                } else {
                    sender.sendMessage("§c§lJogador não encontrado!");
                }
            }
        }
        return true;
    }
}
