package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class PrivateMSG implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("privatemsg") || command.getName().equalsIgnoreCase("pm") || command.getName().equalsIgnoreCase("msg") || command.getName().equalsIgnoreCase("tell") || command.getName().equalsIgnoreCase("whisper")) {
            if (strings.length < 2) {
                if (commandSender instanceof Player) {
                    LanguageManager.getInstance().sendMessage((Player) commandSender, "privatemsg.usage", command.getName());
                } else {
                    commandSender.sendMessage("§cCorrect usage: /" + command.getName() + " <player> <message>");
                }
                return true;
            }

            Player target = Bukkit.getPlayer(strings[0]);
            if (target == null) {
                if (commandSender instanceof Player) {
                    LanguageManager.getInstance().sendMessage((Player) commandSender, "general.player_not_found");
                } else {
                    commandSender.sendMessage("§cPlayer not found.");
                }
                return true;
            }

            String message = "";
            for (int i = 1; i < strings.length; i++) {
                message += strings[i] + " ";
            }

            // Send message to target in their language
            if (target instanceof Player) {
                LanguageManager.getInstance().sendMessage(target, "privatemsg.received", commandSender.getName(), message);
            }

            // Send confirmation to sender in their language
            if (commandSender instanceof Player) {
                LanguageManager.getInstance().sendMessage((Player) commandSender, "privatemsg.sent", target.getName(), message);
            } else {
                commandSender.sendMessage("§a[Private message to " + target.getName() + "] §f» " + message);
            }
            return true;
        }
        return false;
    }

}
