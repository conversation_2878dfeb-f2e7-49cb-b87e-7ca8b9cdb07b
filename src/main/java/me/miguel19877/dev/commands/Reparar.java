package me.miguel19877.dev.commands;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Reparar implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("reparar")) {
            if (commandSender instanceof Player) {
                Player p = (Player) commandSender;
                int rankId = RankSystem.getRankId(p);
                int custoBase = 100;
                int custo = custoBase * rankId;
                if (p.getInventory().getItemInHand().getType().getMaxDurability() > 0) {
                    if (Economy.getMoney(p.getUniqueId()) >= custo) {
                        p.getInventory().getItemInHand().setDurability((short) 0);
                        p.updateInventory();
                        Economy.removeMoney(p.getUniqueId(), (long) custo);
                        LanguageManager.getInstance().sendMessage(p, "reparar.success", String.valueOf(custo));
                    } else {
                        LanguageManager.getInstance().sendMessage(p, "reparar.insufficient_funds");
                    }
                } else {
                    LanguageManager.getInstance().sendMessage(p, "reparar.no_repair_needed");
                }
            }
        }
        return false;
    }
}
