package me.miguel19877.dev.commands;

import me.miguel19877.dev.events.PvPEvent;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

public class PvPEventCommand implements CommandExecutor {
    private final JavaPlugin plugin;
    private final PvPEvent pvpEvent;

    public PvPEventCommand(JavaPlugin plugin) {
        this.plugin = plugin;
        this.pvpEvent = PvPEvent.getInstance(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            sendHelp(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "start":
                if (Permissions.getGrupoId(player) < 13) {
                    player.sendMessage("§cNão tens permissão para iniciar o evento!");
                    return true;
                }
                if (pvpEvent.isEventActive()) {
                    player.sendMessage("§cJá existe um evento ativo!");
                    return true;
                }
                pvpEvent.startEvent();
                plugin.getServer().broadcastMessage("");
                plugin.getServer().broadcastMessage("§6§lEvento 1v1 PvP iniciado! §eTens 5 minutos para te juntares usando /1v1 join!");
                plugin.getServer().broadcastMessage("");
                break;

            case "setloc1":
                if (Permissions.getGrupoId(player) < 13) {
                    player.sendMessage("§cNão tens permissão para definir localizações!");
                    return true;
                }
                pvpEvent.setSpawnLocation1(player.getLocation());
                player.sendMessage("§aLocalização de spawn 1 definida!");
                break;

            case "setloc2":
                if (Permissions.getGrupoId(player) < 13) {
                    player.sendMessage("§cNão tens permissão para definir localizações!");
                    return true;
                }
                pvpEvent.setSpawnLocation2(player.getLocation());
                player.sendMessage("§aLocalização de spawn 2 definida!");
                break;

            case "lobby":
                if (args.length == 1) {
                    if (pvpEvent.getLobbyLocation() == null) {
                        player.sendMessage("§cA localização do lobby ainda não foi definida!");
                        return true;
                    }
                    player.teleport(pvpEvent.getLobbyLocation());
                    player.sendMessage("§aTeleportado para o lobby do evento!");
                } else if (args[1].equalsIgnoreCase("set") && Permissions.getGrupoId(player) >= 13) {
                    pvpEvent.setLobbyLocation(player.getLocation());
                    player.sendMessage("§aLocalização do lobby definida!");
                }
                break;

            case "join":
                if (!pvpEvent.isEventActive()) {
                    player.sendMessage("§cNão existe nenhum evento ativo para te juntares!");
                    return true;
                }
                if (!pvpEvent.isEntryPhase()) {
                    player.sendMessage("§cA fase de inscrição terminou! Já não te podes juntar ao evento.");
                    return true;
                }
                pvpEvent.addParticipant(player);
                break;

            default:
                sendHelp(player);
                break;
        }

        return true;
    }

    private void sendHelp(Player player) {
        LanguageManager langManager = LanguageManager.getInstance();
        langManager.sendMessage(player, "pvpevent.help.title");
        if (Permissions.getGrupoId(player) >= 13) {
            langManager.sendMessage(player, "pvpevent.help.start");
            langManager.sendMessage(player, "pvpevent.help.setloc1");
            langManager.sendMessage(player, "pvpevent.help.setloc2");
            langManager.sendMessage(player, "pvpevent.help.lobby_set");
        }
        langManager.sendMessage(player, "pvpevent.help.join");
        langManager.sendMessage(player, "pvpevent.help.lobby");
    }
}