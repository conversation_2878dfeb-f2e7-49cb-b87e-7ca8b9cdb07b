package me.miguel19877.dev.commands;

import me.miguel19877.dev.<PERSON><PERSON>anager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class SetarKit implements CommandExecutor {


    @Override
    public boolean onCommand(CommandSender sender, Command command, String s, String[] args) {
        if (!(sender instanceof Player)) return true;

        Player player = (Player) sender;
        LanguageManager langManager = LanguageManager.getInstance();

        if (args.length != 3) {
            langManager.sendMessage(player, "setarkit.usage");
            return true;
        }

        String type = args[0];
        int level = Integer.parseInt(args[1]);
        int cooldownHours = Integer.parseInt(args[2]);

        if (Permissions.getGrupoId(player) < 13) {
            langManager.sendMessage(player, "setarkit.no_permission");
            return true;
        }

        KitManager.setKit(player, level, type, cooldownHours * 60);
        langManager.sendMessage(player, "setarkit.success", type, String.valueOf(level), String.valueOf(cooldownHours));
        return true;
    }
}
