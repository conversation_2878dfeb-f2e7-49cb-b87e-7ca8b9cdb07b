package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.CombatLogManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.TPAManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class TPACommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length != 1) {
            String usage = LanguageManager.getInstance().getMessage(player, "tpa.usage");
            if (usage == null) {
                usage = "§cUso: /tpa <jogador>";
            }
            player.sendMessage(usage);
            return true;
        }
        
        // Check if player is in combat
        if (CombatLogManager.getInstance().isInCombat(player)) {
            String combatMessage = LanguageManager.getInstance().getMessage(player, "tpa.cannot_use_in_combat");
            if (combatMessage == null) {
                combatMessage = "§cNão podes usar TPA enquanto estás em combate!";
            }
            player.sendMessage(combatMessage);
            return true;
        }
        
        String targetName = args[0];
        Player target = Bukkit.getPlayer(targetName);
        
        if (target == null || !target.isOnline()) {
            String notFoundMessage = LanguageManager.getInstance().getMessage(player, "tpa.player_not_found", targetName);
            if (notFoundMessage == null) {
                notFoundMessage = "§cJogador §6" + targetName + " §cnão encontrado ou não está online!";
            }
            player.sendMessage(notFoundMessage);
            return true;
        }
        
        if (target.getUniqueId().equals(player.getUniqueId())) {
            String selfMessage = LanguageManager.getInstance().getMessage(player, "tpa.cannot_teleport_to_self");
            if (selfMessage == null) {
                selfMessage = "§cNão podes te teleportar para ti mesmo!";
            }
            player.sendMessage(selfMessage);
            return true;
        }
        
        TPAManager tpaManager = TPAManager.getInstance();
        
        // Check if target already has a pending request
        if (tpaManager.hasPendingRequest(target)) {
            String pendingMessage = LanguageManager.getInstance().getMessage(player, "tpa.target_has_pending", target.getName());
            if (pendingMessage == null) {
                pendingMessage = "§c" + target.getName() + " já tem um pedido de teleporte pendente!";
            }
            player.sendMessage(pendingMessage);
            return true;
        }
        
        // Send TPA request
        boolean success = tpaManager.sendTPARequest(player, target);
        
        if (!success) {
            String errorMessage = LanguageManager.getInstance().getMessage(player, "tpa.request_failed");
            if (errorMessage == null) {
                errorMessage = "§cFalha ao enviar pedido de teleporte. Tenta novamente.";
            }
            player.sendMessage(errorMessage);
        }
        
        return true;
    }
}
