package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Votar implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("votar")) {
            if (commandSender instanceof Player) {
                Player player = (Player) commandSender;
                LanguageManager.getInstance().sendMessage(player, "votar.available_soon");
            } else {
                commandSender.sendMessage("Available soon at craftandhelps.com");
            }
            return true;
        }
        return false;
    }
}
