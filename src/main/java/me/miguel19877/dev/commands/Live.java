package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.TwitchManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;

public class Live implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("live")) {
            if (strings.length == 0) {
                ArrayList<String> lista = TwitchManager.getChannelLive();
                if (lista.isEmpty()) {
                    commandSender.sendMessage("§cNenhum streamer está ao vivo.");
                    return true;
                }
                commandSender.sendMessage("§aStreamers ao vivo:");
                for (String channel : lista) {
                    commandSender.sendMessage("§a- " + channel);
                }
                return true;
            }

            if (strings.length == 1) {
                if (!(commandSender instanceof Player)) {
                    commandSender.sendMessage("§cApenas jogadores podem executar este comando.");
                    return true;
                }
                if (strings[0].equalsIgnoreCase("comecar")) {
                    Player p = (Player) commandSender;
                    if (Permissions.getGrupoId(p) < 8) {
                        p.sendMessage("§cApenas §5[STREAMER] §cou superiores podem executar este comando.");
                        return true;
                    }

                    String canal = TwitchManager.getTwitchChannel(p.getUniqueId());
                    if (TwitchManager.getChannelLive().contains(canal)) {
                        p.sendMessage("§cJá estás em live!");
                        return true;
                    }
                    TwitchManager.setChannelLive(canal);
                    Bukkit.broadcastMessage("");
                    Bukkit.broadcastMessage("§aO streamer " + p.getName() + " começou a live!");
                    Bukkit.broadcastMessage("§bVisualiza-o em: https://twitch.tv/" + canal + " para ganhar recompensas");
                    Bukkit.broadcastMessage("");
                    return true;
                }
            }
        }
        return false;
    }
}
