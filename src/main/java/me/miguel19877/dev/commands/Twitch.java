package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.TwitchManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;

public class Twitch implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cOnly players can execute this command.");
            return true;
        }

        Player player = (Player) sender;
        UUID playerId = player.getUniqueId();

        if (args.length == 0) {
            LanguageManager.getInstance().sendMessage(player, "twitch.usage");
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "vincular":
                if (args.length != 2) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.usage_link");
                    return true;
                }
                String twitchChannel = args[1];

                // Verificar se o canal já está vinculado a outro jogador
                if (TwitchManager.checkTwitchChannel(twitchChannel)) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.channel_already_linked");
                    return true;
                }

                if (TwitchManager.checkPlayerTwitch(playerId)) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.already_linked");
                    return true;
                }

                // Gerar código de verificação
                String verificationCode = TwitchManager.generateVerificationCode(player, twitchChannel);
                LanguageManager.getInstance().sendMessage(player, "twitch.verification_code", verificationCode);
                LanguageManager.getInstance().sendMessage(player, "twitch.add_code_instruction");
                return true;

            case "verificar":
                if (TwitchManager.checkPlayerTwitch(playerId)) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.already_linked");
                    return true;
                }
                TwitchManager.verifyVerificationCode(player);
                return true;

            case "desvincular":
                if (!TwitchManager.checkPlayerTwitch(playerId)) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.not_linked");
                    return true;
                }

                // Desvincular o canal Twitch do jogador
                TwitchManager.removeTwitchChannel(playerId);
                LanguageManager.getInstance().sendMessage(player, "twitch.unlinked_success");
                return true;
            case "setar":
                if (Permissions.getGrupoId(player) < 12) {
                    LanguageManager.getInstance().sendMessage(player, "general.no_permission");
                    return true;
                }

                if (args.length != 3) {
                    LanguageManager.getInstance().sendMessage(player, "twitch.usage_set");
                    return true;
                }

                UUID offlineId = UUID.nameUUIDFromBytes(("OfflinePlayer:" + args[1]).getBytes());

                TwitchManager.assignTwitchChannelToPlayer(offlineId, args[2]);
                LanguageManager.getInstance().sendMessage(player, "twitch.set_success");
            default:
                LanguageManager.getInstance().sendMessage(player, "twitch.usage");
                return true;
        }
    }
}
