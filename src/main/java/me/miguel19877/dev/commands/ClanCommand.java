package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.ClanManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.listeners.AchievementClanListener;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class ClanCommand implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (sender instanceof Player) {
            Player player = (Player) sender;
            LanguageManager langManager = LanguageManager.getInstance();

            if (args.length == 0) {
                langManager.sendMessage(player, "clan.usage");
                return true;
            }

            String subCommand = args[0];

            switch (subCommand.toLowerCase()) {
                case "criar":
                    if (args.length < 2) {
                        langManager.sendMessage(player, "clan.create_usage");
                    } else if (ClanManager.isPlayerInClan(player.getUniqueId())) {
                        langManager.sendMessage(player, "clan.already_in_clan");
                    } else {
                        String clanName = args[1];
                        boolean success = ClanManager.createClan(clanName, player);
                        if (success) {
                            langManager.sendMessage(player, "clan.created", clanName);
                            AchievementClanListener.onPlayerCreateClan(player);
                        } else {
                            langManager.sendMessage(player, "clan.name_exists");
                        }
                    }
                    break;
                case "convidar":
                    if (args.length < 2) {
                        langManager.sendMessage(player, "clan.invite_usage");
                    } else if (!ClanManager.isPlayerInClan(player.getUniqueId())) {
                        langManager.sendMessage(player, "clan.not_in_clan");
                    }else if (!ClanManager.isPlayerClanLeader(player.getUniqueId())) {
                        langManager.sendMessage(player, "clan.leader_only");
                    }else {
                        String clanName = ClanManager.getClanName(player.getUniqueId());
                        String invitedPlayerName = args[1];
                        Player invitedPlayer = player.getServer().getPlayer(invitedPlayerName);
                        if (invitedPlayer == null) {
                            langManager.sendMessage(player, "clan.player_not_online");
                        } else {
                            if (!ClanManager.hasInvite(invitedPlayer.getUniqueId(), clanName)) {
                                ClanManager.invitePlayerToClan(clanName, invitedPlayer);
                                player.sendMessage("§6§lClans §7§l> Convite enviado a " + invitedPlayerName);
                                invitedPlayer.sendMessage("§6§lClans §7§l> Recebeste um convite para entrar no clan " + clanName);
                            } else {
                                player.sendMessage("§6§lClans §7§l> O jogador já tem um convite para entrar no clan " + clanName);
                            }
                        }
                    }
                    break;
                case "entrar":
                    if (args.length < 2) {
                        player.sendMessage("§6§lClans §7§l> Usa: /clan entrar <name>");
                    } else if (ClanManager.isPlayerInClan(player.getUniqueId())) {
                        player.sendMessage("§6§lClans §7§l> Já estás num clan.");
                    } else {
                        String clanName = args[1];
                        if (ClanManager.hasInvite(player.getUniqueId(), clanName)) {
                            ClanManager.addPlayerToClan(clanName, player);
                            player.sendMessage("§6§lClans §7§l> Entraste no clan " + clanName);
                            AchievementClanListener.onPlayerJoinClan(player);
                        } else {
                            player.sendMessage("§6§lClans §7§l> Não tens convite para entrar no clan " + clanName);
                        }
                    }
                    break;

                case "sair":
                    if (!ClanManager.isPlayerInClan(player.getUniqueId())) {
                        player.sendMessage("§6§lClans §7§l> Não estás num clan.");
                    } else if (ClanManager.isPlayerClanLeader(player.getUniqueId())) {
                        player.sendMessage("§6§lClans §7§l> Não podes sair do clan porque és o líder.");
                    } else {
                        ClanManager.removePlayerFromClan(player);
                        player.sendMessage("§6§lClans §7§l> Saíste do clan.");
                    }
                    break;

                case "apagar":
                    if (!ClanManager.isPlayerClanLeader(player.getUniqueId())) {
                        player.sendMessage("§6§lClans §7§l> Apenas o líder do clan pode apagar o clan.");
                    } else {
                        String clanName = ClanManager.getClanName(player.getUniqueId());
                        ClanManager.deleteClan(clanName);
                        player.sendMessage("§6§lClans §7§l> Clan apagado.");
                    }
                    break;

                default:
                    player.sendMessage("Unknown command. Usage: /clan <create|join|leave|delete> [name]");
                    break;
            }
        }
        return false;
    }
}
