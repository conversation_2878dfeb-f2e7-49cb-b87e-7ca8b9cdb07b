package me.miguel19877.dev.commands;

import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.listeners.PortalListener;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Mina implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender commandSender, Command command, String s, String[] strings) {
        if (command.getName().equalsIgnoreCase("mina")) {
            Player p = (Player) commandSender;
            int rankid = RankSystem.getRankId(p);
            PortalListener.teleportToMinas(p, rankid);
            LanguageManager.getInstance().sendMessage(p, "mina.teleported");
        }
        return false;
    }
}
