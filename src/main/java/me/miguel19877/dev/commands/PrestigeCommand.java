package me.miguel19877.dev.commands;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.PrestigeManager;
import me.miguel19877.dev.listeners.AchievementRankListener;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.utils.TitleAPI;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class PrestigeCommand implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("This command can only be run by a player.");
            return true;
        }

        Player player = (Player) sender;
        int rankId = RankSystem.getRankId(player);

        if (rankId == 22) {
            // Reset rank to 1
            RankSystem.setRank(player, 1);
            // Reset money balance to 0
            Economy.setMoney(player.getUniqueId(), 0L);
            // Add 1 prestige level
            PrestigeManager.addPrestigeLevel(player);

            // Make a Title announcement
            for (Player onlinePlayer : player.getServer().getOnlinePlayers()) {
                String titleText = LanguageManager.getInstance().getMessage(onlinePlayer, "prestige.title_announcement");
                String subtitleText = LanguageManager.getInstance().getMessage(onlinePlayer, "prestige.subtitle_announcement", player.getName());
                TitleAPI.sendTitle(onlinePlayer, 20, 60, 20, titleText, subtitleText);
            }

            // Notify the player
            LanguageManager.getInstance().sendMessage(player, "prestige.success_message", String.valueOf(PrestigeManager.getPrestigeLevel(player)));

            // Check prestige achievements
            AchievementRankListener.checkPrestigeAchievements(player);
        } else {
            LanguageManager.getInstance().sendMessage(player, "prestige.not_max_rank");
        }

        return true;
    }
}