package me.miguel19877.dev.commands;

import me.miguel19877.dev.<PERSON><PERSON>anager;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.Date;

public class Kit implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("kit")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                p.openInventory(Rankup.kitInventory);
            } else {
                sender.sendMessage("§cCommand only for players.");
            }
        }
        return true;
    }


}
