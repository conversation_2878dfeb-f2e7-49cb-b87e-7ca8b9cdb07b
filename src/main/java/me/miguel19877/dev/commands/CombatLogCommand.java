package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.CombatLogManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;

public class CombatLogCommand implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check if player has admin permissions
        if (Permissions.getGrupoId(player) < 13) {
            player.sendMessage("§cNão tens permissão para usar este comando!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        CombatLogManager combatManager = CombatLogManager.getInstance();
        
        switch (args[0].toLowerCase()) {
            case "test":
                if (args.length < 2) {
                    player.sendMessage("§cUso: /combatlog test <jogador>");
                    return true;
                }
                
                Player target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    player.sendMessage("§cJogador não encontrado!");
                    return true;
                }
                
                combatManager.putInCombat(target);
                player.sendMessage("§a" + target.getName() + " foi colocado em combate para teste!");
                break;
                
            case "check":
                if (args.length < 2) {
                    player.sendMessage("§cUso: /combatlog check <jogador>");
                    return true;
                }
                
                Player checkTarget = Bukkit.getPlayer(args[1]);
                if (checkTarget == null) {
                    player.sendMessage("§cJogador não encontrado!");
                    return true;
                }
                
                boolean inCombat = combatManager.isInCombat(checkTarget);
                int remainingTime = combatManager.getRemainingCombatTime(checkTarget);
                
                if (inCombat) {
                    player.sendMessage("§e" + checkTarget.getName() + " está em combate! Tempo restante: " + remainingTime + "s");
                } else {
                    player.sendMessage("§a" + checkTarget.getName() + " não está em combate.");
                }
                break;
                
            case "list":
                Map<UUID, Long> combatPlayers = combatManager.getCombatLoggedPlayers();
                if (combatPlayers.isEmpty()) {
                    player.sendMessage("§aNenhum jogador está em combate no momento.");
                } else {
                    player.sendMessage("§6Jogadores em combate:");
                    for (UUID playerId : combatPlayers.keySet()) {
                        Player combatPlayer = Bukkit.getPlayer(playerId);
                        if (combatPlayer != null) {
                            int remaining = combatManager.getRemainingCombatTime(combatPlayer);
                            player.sendMessage("§7- " + combatPlayer.getName() + " (" + remaining + "s restantes)");
                        }
                    }
                }
                break;
                
            case "clear":
                if (args.length < 2) {
                    player.sendMessage("§cUso: /combatlog clear <jogador>");
                    return true;
                }
                
                Player clearTarget = Bukkit.getPlayer(args[1]);
                if (clearTarget == null) {
                    player.sendMessage("§cJogador não encontrado!");
                    return true;
                }
                
                combatManager.removeFromCombat(clearTarget.getUniqueId());
                player.sendMessage("§a" + clearTarget.getName() + " foi removido do combate!");
                break;
                
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage("§6§lCombat Log Commands:");
        player.sendMessage("§7/combatlog test <jogador> - Colocar jogador em combate para teste");
        player.sendMessage("§7/combatlog check <jogador> - Verificar status de combate");
        player.sendMessage("§7/combatlog list - Listar todos os jogadores em combate");
        player.sendMessage("§7/combatlog clear <jogador> - Remover jogador do combate");
    }
}
