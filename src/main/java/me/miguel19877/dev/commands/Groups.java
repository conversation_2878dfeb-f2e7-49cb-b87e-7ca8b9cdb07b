package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.UUID;

public class Groups implements CommandExecutor {
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("grupo")) {
            if (sender instanceof Player) {
                Player p = (Player) sender;
                if (Permissions.getGrupoId(p) >= 13) {
                    if (args.length == 0) {
                        LanguageManager langManager = LanguageManager.getInstance();
                        langManager.sendMessage(p, "groups.title");
                        langManager.sendMessage(p, "groups.set_usage");
                        langManager.sendMessage(p, "groups.list_usage");
                    }else if (args.length == 3) {
                        if (args[0].equalsIgnoreCase("setar")) {
                            LanguageManager langManager = LanguageManager.getInstance();
                            if (Bukkit.getPlayer(args[1]) != null) {
                                Permissions.setGrupo(Bukkit.getPlayer(args[1]), Integer.parseInt(args[2]));
                                langManager.sendMessage(p, "groups.set_success", args[1], args[2]);
                            }else {
                                OfflinePlayer target = Bukkit.getOfflinePlayer(UUID.nameUUIDFromBytes(("OfflinePlayer:" + args[1]).getBytes()));
                                Permissions.setGrupoOffline(target, Integer.parseInt(args[2]));
                                langManager.sendMessage(p, "groups.set_success", args[1], args[2]);
                            }
                        }
                    }else {
                        LanguageManager langManager = LanguageManager.getInstance();
                        langManager.sendMessage(p, "groups.title");
                        langManager.sendMessage(p, "groups.set_usage");
                    }
                }else {
                    LanguageManager.getInstance().sendMessage(p, "general.no_permission");
                }
            }else {
                if (args[0].equalsIgnoreCase("setar")) {
                    if (Bukkit.getPlayer(args[1]) != null) {
                        Permissions.setGrupo(Bukkit.getPlayer(args[1]), Integer.parseInt(args[2]));
                        sender.sendMessage("§6§lRanks §8» §7Grupo de §f" + args[1] + " §7definido para §f" + args[2]);
                    }else {
                        OfflinePlayer target = Bukkit.getOfflinePlayer(UUID.nameUUIDFromBytes(("OfflinePlayer:" + args[1]).getBytes()));
                        Permissions.setGrupoOffline(target, Integer.parseInt(args[2]));
                        sender.sendMessage("§6§lRanks §8» §7Grupo de §f" + args[1] + " §7definido para §f" + args[2]);
                    }
                }
            }

        }
        return false;
    }
}
