package me.miguel19877.dev.permissions;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.RedisManager;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.UUID;

public class Permissions {
    private static final HashMap<UUID, Integer> grupo = new HashMap<>();
    private static final String GRUPO_KEY = "grupo:";

    private static final int DEFAULT_GROUP_ID = 1;
    private static final String DEFAULT_GROUP_NAME = "§8[Membro]";

    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String group = jedis.get(GRUPO_KEY + playerId);
            grupo.put(playerId, group != null ? Integer.parseInt(group) : DEFAULT_GROUP_ID);
        }
    }

    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        Integer grupoid = grupo.get(playerId);
        if (grupoid != null) {
            try (Jedis jedis = getJedis()) {
                jedis.set(GRUPO_KEY + playerId, String.valueOf(grupoid));
            }
        }
    }

    public static Integer getGrupoId(Player player) {
        return grupo.getOrDefault(player.getUniqueId(), DEFAULT_GROUP_ID);
    }

    public static String getGrupo(Player player) {
        Integer grupoid = getGrupoId(player);
        return getGroupName(grupoid);
    }

    public static String getGroupName(int grupoid) {
        switch (grupoid) {
            case 1: return "§8[Membro]";
            case 2: return "§7[VIP]";
            case 3: return "§7[VIP+]";
            case 4: return "§7[VIP PRO]";
            case 5: return "§7[VIP SUPER]";
            case 6: return "§7[VIP GOD]";
            case 7: return "§4[Y§fT]";
            case 8: return "§5[STREAMER]";
            case 9: return "§c[uP]";
            case 10: return "§e[HELPER]";
            case 11: return "§a[MOD]";
            case 12: return "§6[ADM]";
            case 13: return "§b[DEV]";
            case 14: return "§4[DONO]";
            default: return DEFAULT_GROUP_NAME;
        }
    }

    public static void setGrupo(Player player, int grupoid) {
        grupo.put(player.getUniqueId(), grupoid);
        savePlayer(player);
        Rankup.getInstance().deltaManager.recordGroupIdChange(player.getUniqueId(), grupoid);
    }

    public static void setGrupoOffline(OfflinePlayer player, int grupoid) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.set(GRUPO_KEY + playerId, String.valueOf(grupoid));
        }
        Rankup.getInstance().deltaManager.recordGroupIdChange(playerId, grupoid);
    }

    public static boolean vipExists(String grupo) {
        switch (grupo.toLowerCase()) {
            case "vip":
            case "vip+":
            case "vippro":
            case "vipsuper":
            case "vipgod":
                return true;
            default:
                return false;
        }
    }
    
    public static int convertVipToId(String vip) {
        switch (vip.toLowerCase()) {
            case "vip":
                return 2;
            case "vip+":
                return 3;
            case "vippro":
                return 4;
            case "vipsuper":
                return 5;
            case "vipgod":
                return 6;
            default:
                throw new IllegalArgumentException("Invalid VIP name: " + vip);
        }
    }


    public static void removePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        grupo.remove(playerId);
        try (Jedis jedis = getJedis()) {
            jedis.del(GRUPO_KEY + playerId);
        }
    }
}