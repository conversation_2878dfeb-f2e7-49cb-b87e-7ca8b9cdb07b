package me.miguel19877.dev.Tasks;

import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.FallingBlock;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityChangeBlockEvent;
import org.bukkit.event.server.PluginDisableEvent;

public class FallBlock implements Listener {

    public FallBlock(Rankup plugin) {
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
        plugin.getServer().getWorld("mundo").spawnFallingBlock(new Location(plugin.getServer().getWorld("mundo"), 249, 27, 229), Material.ENDER_STONE, (byte) 0);
        plugin.getServer().getWorld("mundo").spawnFallingBlock(new Location(plugin.getServer().getWorld("mundo"), 262, 33, 228), Material.WOOD, (byte) 2);
        plugin.getServer().getWorld("mundo").spawnFallingBlock(new Location(plugin.getServer().getWorld("mundo"), 262, 33, 220), Material.WOOD, (byte) 2);
    }

    @EventHandler
    public void onEntityChange(EntityChangeBlockEvent event) {
        if (event.getTo() == Material.ENDER_STONE) {
            event.getBlock().getWorld().spawnFallingBlock(new Location(event.getBlock().getWorld(), 249, 27, 229), Material.ENDER_STONE, (byte) 0);
            event.getEntity().remove();
            event.setCancelled(true);
        }
        if (event.getTo() == Material.WOOD) {
            event.getBlock().getWorld().spawnFallingBlock(event.getBlock().getLocation().add(0, 3,0), Material.WOOD, (byte) 2);
            event.getEntity().remove();
            event.setCancelled(true);
        }
    }

}
