package me.miguel19877.dev.Tasks;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.listeners.CustomTabScoreboard;
import me.miguel19877.dev.managers.ClanManager;
import me.miguel19877.dev.managers.KillDeathManager;
import me.miguel19877.dev.managers.TwitchManager;
import me.miguel19877.dev.managers.VIPManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitRunnable;
import redis.clients.jedis.Tuple;

import java.util.*;

public class Leaderboards extends BukkitRunnable {

    /**
     * An array of inventory slots where the leaderboards items will be placed.
     */
    int[] slots = {13, 21, 23, 29, 33};

    @Override
    public void run() {
        // TwitchManager data is now managed via PlayerData system, no need to save manually
        Bukkit.getScheduler().runTask(Rankup.getInstance(), () -> {
            for (Player player : Bukkit.getOnlinePlayers()) {
                VIPManager.loadPlayer(player);
            }
        });

        // Retrieve the top 5 players by money from the Economy manager.
        Set<Tuple> set = Economy.getTop5Money();
        List<Tuple> top5Money = new ArrayList<>();
        // Convert the Set of Tuples (element, score) to a List for easier indexing.
        for (Tuple tuple : set) {
            top5Money.add(new Tuple(tuple.getElement(), tuple.getScore()));
        }
        // Update the top money leaderboard inventory.
        updateTopMoneyInventory(Rankup.topMoneyInventory, top5Money);

        // Retrieve the top 5 players by kills from the KillDeathManager.
        Set<Tuple> setKills = KillDeathManager.getTop5Kills();
        List<Tuple> top5Kills = new ArrayList<>();
        // Convert the Set of Tuples (element, score) to a List for easier indexing.
        for (Tuple tuple : setKills) {
            top5Kills.add(new Tuple(tuple.getElement(), tuple.getScore()));
        }
        // Update the top kills leaderboard inventory.
        updateTopKills(Rankup.topKillsInventory, top5Kills);

        // Retrieve the top 5 clans by kills from the ClanManager.
        Set<Tuple> setClanKills = ClanManager.getTop5Kills();
        List<Tuple> top5ClanKills = new ArrayList<>();
        // Convert the Set of Tuples (element, score) to a List for easier indexing.
        for (Tuple tuple : setClanKills) {
            top5ClanKills.add(new Tuple(tuple.getElement(), tuple.getScore()));
        }
        // Update the top clan kills leaderboard inventory.
        updateTopClanKills(Rankup.topClanKillsInventory, top5ClanKills);
    }

    /**
     * Updates the provided inventory with the top 5 money earners.
     * Each entry is represented by a player head with their name and money amount.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topMoneyInventory The inventory to update.
     * @param top5Money A list of {@link Tuple} objects, where each tuple contains
     *                  the UUID of the player (as a String) and their money score (as a double).
     */
    public void updateTopMoneyInventory(Inventory topMoneyInventory, List<Tuple> top5Money) {
        for (int i = 0; i < slots.length; i++) {
            // Create a new player head item.
            // The data value (short) 3 is used for player heads.
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
        SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a player for the current rank.
            if (i < top5Money.size()) {
                // Get the UUID from the tuple and convert it to a UUID object.
                UUID uuid = UUID.fromString(top5Money.get(i).getElement());
                // Set the owner of the skull to the player's name.
                skullMeta.setOwner(Bukkit.getOfflinePlayer(uuid).getName()); // Note: This might be null if player never joined.
                // Set the display name of the item.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + Bukkit.getOfflinePlayer(uuid).getName()); // Note: This might be null.
                // Set the lore (description) of the item with the player's money.
                skullMeta.setLore(Collections.singletonList("§a§l" + CustomTabScoreboard.convert((long) top5Money.get(i).getScore()) + "€")); // Format money.
            } else {
                // If no player for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topMoneyInventory.setItem(slots[i], skull);
        }
    }

    /**
     * Updates the provided inventory with the top 5 players by kills.
     * Each entry is represented by a player head with their name and kill count.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topKillsInventory The inventory to update.
     * @param top5Kills A list of {@link Tuple} objects, where each tuple contains
     *                  the UUID of the player (as a String) and their kill score (as a double).
     */
    public void updateTopKills(Inventory topKillsInventory, List<Tuple> top5Kills) {
        for (int i = 0; i < slots.length; i++) {
            // Create a new player head item.
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a player for the current rank.
            if (i < top5Kills.size()) {
                // Get the UUID from the tuple and convert it to a UUID object.
                UUID uuid = UUID.fromString(top5Kills.get(i).getElement());
                // Set the owner of the skull to the player's name.
                skullMeta.setOwner(Bukkit.getOfflinePlayer(uuid).getName()); // Note: This might be null.
                // Set the display name of the item.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + Bukkit.getOfflinePlayer(uuid).getName()); // Note: This might be null.
                // Set the lore (description) of the item with the player's kill count.
                skullMeta.setLore(Collections.singletonList("§c§l" + (int) top5Kills.get(i).getScore() + " Kills")); // Cast score to int for kills.
            } else {
                // If no player for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A Kills"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topKillsInventory.setItem(slots[i], skull);
        }
    }

    /**
     * Updates the provided inventory with the top 5 clans by kills.
     * Each entry is represented by a player head (using the clan name as owner, which might not display correctly as a player head)
     * with the clan name and kill count.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topMoneyInventory The inventory to update (named topMoneyInventory, but used for clan kills).
     * @param top5Money A list of {@link Tuple} objects, where each tuple contains
     *                  the clan name (as a String) and their kill score (as a double).
     */
    public void updateTopClanKills(Inventory topMoneyInventory, List<Tuple> top5Money) {
        for (int i = 0; i < 5; i++) {
            // Create a new player head item.
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a clan for the current rank.
            if (i < top5Money.size()) {
                // Set the owner of the skull to the clan name.
                // Note: Setting owner to a non-player name might result in a default head or no head.
                skullMeta.setOwner(top5Money.get(i).getElement());
                // Set the display name of the item to the clan name.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + top5Money.get(i).getElement());
                // Set the lore (description) of the item with the clan's kill count.
                skullMeta.setLore(Collections.singletonList("§c§l" + (int) top5Money.get(i).getScore() + " Kills"));
            } else {
                // If no clan for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A Kills"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topMoneyInventory.setItem(slots[i], skull);
        }
    }
}
