package me.miguel19877.dev.Tasks;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.BlockChanger;
import me.miguel19877.dev.utils.Cuboid;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

public class Minas extends BukkitRunnable {
    private static final int RESET_INTERVAL_SECONDS = 600;
    private int seconds = RESET_INTERVAL_SECONDS;

    private static final Queue<Location> blockResetQueue = new ConcurrentLinkedQueue<>();
    private static final Queue<Location> physicsBlockQueue = new ConcurrentLinkedQueue<>();
    private static final int BLOCKS_PER_TICK = 500;

    // Mine compositions - each mine has a name and block composition
    private static final Map<String, MineComposition> MINE_COMPOSITIONS = new HashMap<>();

    // Mine cuboids - maps mine names to their cuboid areas
    private static final Map<String, Cuboid> MINE_CUBOIDS = new HashMap<>();

    // Materials that require physics updates (gravity blocks)
    private static final Set<Material> PHYSICS_MATERIALS = new HashSet<>(Arrays.asList(
        Material.SAND,
        Material.GRAVEL,
        Material.ANVIL,
        Material.DRAGON_EGG
    ));

    // Random instance for block selection
    private static final Random RANDOM = new Random();

    public static void start(int delay, int period, HashMap<String, Location> minasrespawn, HashMap<String, Cuboid> minas) {
        // Store mine cuboids
        MINE_CUBOIDS.putAll(minas);

        // Initialize default mine compositions
        initializeDefaultCompositions();
        initializeSpecialMines();

        // Start the mine reset task
        new Minas().runTaskTimer(Rankup.getInstance(), delay, period);
    }

    /**
     * Initialize default mine compositions based on server ranks
     */
    private static void initializeDefaultCompositions() {
        // Novice Mine - Dirt, Soul Sand (50% each)
        MineComposition noviceMine = new MineComposition();
        noviceMine.addBlock(Material.DIRT, 50.0);
        noviceMine.addBlock(Material.SOUL_SAND, 50.0);
        MINE_COMPOSITIONS.put("novato", noviceMine);

        // Beginner Mine - Sand, Red Sand (50% each)
        MineComposition beginnerMine = new MineComposition();
        beginnerMine.addBlock(Material.SAND, (byte) 0, 50.0);     // Regular sand
        beginnerMine.addBlock(Material.SAND, (byte) 1, 50.0);     // Red sand (data value 1)
        MINE_COMPOSITIONS.put("iniciante", beginnerMine);

        // Student Mine - All wood types (equal distribution)
        MineComposition studentMine = new MineComposition();
        studentMine.addBlock(Material.LOG, (byte) 0, 16.67);      // Oak Wood
        studentMine.addBlock(Material.LOG, (byte) 1, 16.67);      // Spruce Wood
        studentMine.addBlock(Material.LOG, (byte) 2, 16.67);      // Birch Wood
        studentMine.addBlock(Material.LOG, (byte) 3, 16.67);      // Jungle Wood
        studentMine.addBlock(Material.LOG_2, (byte) 0, 16.67);    // Acacia Wood
        studentMine.addBlock(Material.LOG_2, (byte) 1, 16.65);    // Dark Oak Wood - remaining %
        MINE_COMPOSITIONS.put("estudante", studentMine);

        // Apprentice Mine - Stone, Cobblestone, Andesite (equal distribution)
        MineComposition apprenticeMine = new MineComposition();
        apprenticeMine.addBlock(Material.STONE, (byte) 0, 33.33);     // Regular Stone
        apprenticeMine.addBlock(Material.COBBLESTONE, (byte) 0, 33.33); // Cobblestone
        apprenticeMine.addBlock(Material.STONE, (byte) 5, 33.34);     // Andesite (data 5)
        MINE_COMPOSITIONS.put("aprendiz", apprenticeMine);

        // Intern Mine - Bricks, Mossy Stone, Stone Bricks (equal distribution)
        MineComposition internMine = new MineComposition();
        internMine.addBlock(Material.BRICK, 33.33);
        internMine.addBlock(Material.MOSSY_COBBLESTONE, 33.33);
        internMine.addBlock(Material.SMOOTH_BRICK, 33.34);
        MINE_COMPOSITIONS.put("estagiario", internMine);

        // Employee Mine - Lapis Lazuli Ore, Redstone Ore (50% each)
        MineComposition employeeMine = new MineComposition();
        employeeMine.addBlock(Material.LAPIS_ORE, 50.0);
        employeeMine.addBlock(Material.REDSTONE_ORE, 50.0);
        MINE_COMPOSITIONS.put("empregado", employeeMine);

        // Promoted Mine - Redstone Ore, Coal Ore (50% each)
        MineComposition promotedMine = new MineComposition();
        promotedMine.addBlock(Material.REDSTONE_ORE, 50.0);
        promotedMine.addBlock(Material.COAL_ORE, 50.0);
        MINE_COMPOSITIONS.put("promovido", promotedMine);

        // Reviewer Mine - Coal Ore, Iron Ore (50% each)
        MineComposition reviewerMine = new MineComposition();
        reviewerMine.addBlock(Material.COAL_ORE, 50.0);
        reviewerMine.addBlock(Material.IRON_ORE, 50.0);
        MINE_COMPOSITIONS.put("revisor", reviewerMine);

        // Sub-Manager Mine - Iron Ore, Gold Ore (50% each)
        MineComposition subManagerMine = new MineComposition();
        subManagerMine.addBlock(Material.IRON_ORE, 50.0);
        subManagerMine.addBlock(Material.GOLD_ORE, 50.0);
        MINE_COMPOSITIONS.put("subgerente", subManagerMine);

        // Manager Mine - Nether Rock (100%)
        MineComposition managerMine = new MineComposition();
        managerMine.addBlock(Material.NETHERRACK, 100.0);
        MINE_COMPOSITIONS.put("gerente", managerMine);

        // Sub-Chief Mine - Quartz Ore (100%)
        MineComposition subChiefMine = new MineComposition();
        subChiefMine.addBlock(Material.QUARTZ_ORE, 100.0);
        MINE_COMPOSITIONS.put("subchefe", subChiefMine);

        // Chief Mine - Gold Ore, Diamond Ore (50% each)
        MineComposition chiefMine = new MineComposition();
        chiefMine.addBlock(Material.GOLD_ORE, 50.0);
        chiefMine.addBlock(Material.DIAMOND_ORE, 50.0);
        MINE_COMPOSITIONS.put("chefe", chiefMine);

        // President Mine - Diamond Ore, Emerald Ore (50% each)
        MineComposition presidentMine = new MineComposition();
        presidentMine.addBlock(Material.DIAMOND_ORE, 50.0);
        presidentMine.addBlock(Material.EMERALD_ORE, 50.0);
        MINE_COMPOSITIONS.put("presidente", presidentMine);

        // Millionaire Mine - Emerald Ore (100%)
        MineComposition millionaireMine = new MineComposition();
        millionaireMine.addBlock(Material.EMERALD_ORE, 100.0);
        MINE_COMPOSITIONS.put("milionario", millionaireMine);

        // Billionaire Mine - Lapis Lazuli Block, Redstone Block (50% each)
        MineComposition billionaireMine = new MineComposition();
        billionaireMine.addBlock(Material.LAPIS_BLOCK, 50.0);
        billionaireMine.addBlock(Material.REDSTONE_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("bilionario", billionaireMine);

        // King Mine - Redstone Block, Coal Block (50% each)
        MineComposition kingMine = new MineComposition();
        kingMine.addBlock(Material.REDSTONE_BLOCK, 50.0);
        kingMine.addBlock(Material.COAL_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("rei", kingMine);

        // Divine Mine - Coal Block, Iron Block (50% each)
        MineComposition divineMine = new MineComposition();
        divineMine.addBlock(Material.COAL_BLOCK, 50.0);
        divineMine.addBlock(Material.IRON_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("divino", divineMine);

        // Demigod Mine - Iron Block, Gold Block (50% each)
        MineComposition demigodMine = new MineComposition();
        demigodMine.addBlock(Material.IRON_BLOCK, 50.0);
        demigodMine.addBlock(Material.GOLD_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("semideus", demigodMine);

        // Omniscient Mine - Gold Block, Diamond Block (50% each)
        MineComposition omniscientMine = new MineComposition();
        omniscientMine.addBlock(Material.GOLD_BLOCK, 50.0);
        omniscientMine.addBlock(Material.DIAMOND_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("omniciente", omniscientMine);

        // Omnipotent Mine - Diamond Block, Emerald Block (50% each)
        MineComposition omnipotentMine = new MineComposition();
        omnipotentMine.addBlock(Material.DIAMOND_BLOCK, 50.0);
        omnipotentMine.addBlock(Material.EMERALD_BLOCK, 50.0);
        MINE_COMPOSITIONS.put("omnipotente", omnipotentMine);

        // Omnipresent Mine - Emerald Block (100%)
        MineComposition omnipresentMine = new MineComposition();
        omnipresentMine.addBlock(Material.EMERALD_BLOCK, 100.0);
        MINE_COMPOSITIONS.put("omnipresente", omnipresentMine);

        // God Mine - Obsidian (100%)
        MineComposition godMine = new MineComposition();
        godMine.addBlock(Material.OBSIDIAN, 100.0);
        MINE_COMPOSITIONS.put("deus", godMine);
    }

    /**
     * Initialize special mines (PVP, VIP, YouTuber, etc.)
     */
    private static void initializeSpecialMines() {
        // PVP Mine - Use ORE blocks instead of items (equal distribution)
        MineComposition pvpMine = new MineComposition();
        pvpMine.addBlock(Material.LAPIS_ORE, 14.29);
        pvpMine.addBlock(Material.REDSTONE_ORE, 14.29);
        pvpMine.addBlock(Material.COAL_ORE, 14.29);
        pvpMine.addBlock(Material.IRON_ORE, 14.29);
        pvpMine.addBlock(Material.GOLD_ORE, 14.29);
        pvpMine.addBlock(Material.DIAMOND_ORE, 14.29);
        pvpMine.addBlock(Material.EMERALD_ORE, 14.26); // Remaining percentage
        MINE_COMPOSITIONS.put("minapvp", pvpMine);

        // YouTuber/Streamer Mine - Use ORE blocks (50% each)
        MineComposition youtuberMine = new MineComposition();
        youtuberMine.addBlock(Material.GOLD_ORE, 50.0);
        youtuberMine.addBlock(Material.DIAMOND_ORE, 50.0);
        MINE_COMPOSITIONS.put("ytstreamer", youtuberMine);

        // VIP Mine - Use ORE blocks (50% each)
        MineComposition vipMine = new MineComposition();
        vipMine.addBlock(Material.COAL_ORE, 50.0);
        vipMine.addBlock(Material.IRON_ORE, 50.0);
        MINE_COMPOSITIONS.put("vip", vipMine);

        // VIP+ Mine - Use ORE blocks (50% each)
        MineComposition vipPlusMine = new MineComposition();
        vipPlusMine.addBlock(Material.IRON_ORE, 50.0);
        vipPlusMine.addBlock(Material.GOLD_ORE, 50.0);
        MINE_COMPOSITIONS.put("vipplus", vipPlusMine);

        // VIP PRO Mine - Use ORE blocks (50% each)
        MineComposition vipProMine = new MineComposition();
        vipProMine.addBlock(Material.GOLD_ORE, 50.0);
        vipProMine.addBlock(Material.DIAMOND_ORE, 50.0);
        MINE_COMPOSITIONS.put("vippro", vipProMine);

        // VIP SUPER Mine - Use ORE blocks (50% each)
        MineComposition vipSuperMine = new MineComposition();
        vipSuperMine.addBlock(Material.DIAMOND_ORE, 50.0);
        vipSuperMine.addBlock(Material.EMERALD_ORE, 50.0);
        MINE_COMPOSITIONS.put("vipsuper", vipSuperMine);

        // VIP GOD Mine - Emerald ORE and Obsidian (50% each)
        MineComposition vipGodMine = new MineComposition();
        vipGodMine.addBlock(Material.EMERALD_ORE, 50.0);
        vipGodMine.addBlock(Material.OBSIDIAN, 50.0);
        MINE_COMPOSITIONS.put("vipgod", vipGodMine);

        // Grow UP Mine - Use ORE blocks (50% each)
        MineComposition growUpMine = new MineComposition();
        growUpMine.addBlock(Material.GOLD_ORE, 50.0);
        growUpMine.addBlock(Material.DIAMOND_ORE, 50.0);
        MINE_COMPOSITIONS.put("up", growUpMine);
    }

    private void resetMines() {

        // Find all empty blocks in all mine cuboids and queue them for reset
        int totalEmptyBlocks = 0;


        for (Map.Entry<String, Cuboid> mineEntry : MINE_CUBOIDS.entrySet()) {
            String mineName = mineEntry.getKey();
            Cuboid cuboid = mineEntry.getValue();


            // Get composition for this mine (default to "stone" if not found)
            MineComposition composition = MINE_COMPOSITIONS.getOrDefault(mineName, MINE_COMPOSITIONS.get("stone"));
            if (composition == null) {
                Bukkit.getLogger().warning("No composition found for mine: " + mineName + ", skipping...");
                continue;
            }

            // Find empty blocks in this mine
            List<Location> emptyBlocks = findEmptyBlocks(cuboid);
            totalEmptyBlocks += emptyBlocks.size();

            // Add empty blocks to reset queue with their mine composition
            for (Location location : emptyBlocks) {
                blockResetQueue.add(location);

                // Pre-determine what block this location will become for physics queue
                WeightedBlock selectedBlock = composition.getRandomWeightedBlock();
                if (PHYSICS_MATERIALS.contains(selectedBlock.material)) {
                    physicsBlockQueue.add(location);
                }
            }

        }

        if (totalEmptyBlocks == 0) {
            return;
        }
        // Phase 1: Fill empty blocks with composition-based blocks
        new BukkitRunnable() {
            @Override
            public void run() {
                if (blockResetQueue.isEmpty()) {
                    this.cancel();
                    return;
                }

                int blocksProcessed = 0;
                while (blocksProcessed < BLOCKS_PER_TICK && !blockResetQueue.isEmpty()) {
                    Location location = blockResetQueue.poll();
                    if (location == null) {
                        break;
                    }

                    // Ensure chunk is loaded
                    if (!location.getChunk().isLoaded()) {
                        location.getChunk().load();
                    }

                    // Find which mine this location belongs to and get its composition
                    String mineName = findMineForLocation(location);
                    MineComposition composition = MINE_COMPOSITIONS.getOrDefault(mineName, MINE_COMPOSITIONS.get("stone"));

                    if (composition != null) {
                        try {
                            // Get random block with data based on composition
                            WeightedBlock selectedBlock = composition.getRandomWeightedBlock();
                            ItemStack item = new ItemStack(selectedBlock.material, 1, selectedBlock.data);
                            BlockChanger.setBlock(location, item); // No physics initially
                        } catch (Exception e) {
                            // If block placement fails, try with a simple stone block
                            System.err.println("Failed to place block at " + location + ": " + e.getMessage());
                            try {
                                BlockChanger.setChunkBlock(location, Material.STONE, false);
                            } catch (Exception e2) {
                                System.err.println("Failed to place fallback stone block at " + location + ": " + e2.getMessage());
                            }
                        }
                    }

                    blocksProcessed++;
                }
            }
        }.runTaskTimer(Rankup.getInstance(), 0L, 1L);
    }

    /**
     * Find all empty blocks (air, water, lava) in a cuboid using cuboid.getBlocks()
     */
    private List<Location> findEmptyBlocks(Cuboid cuboid) {
        List<Location> emptyBlocks = new ArrayList<>();

        try {
            // Use cuboid.getBlocks() to get all blocks in the cuboid
            List<Block> allBlocks = cuboid.getBlocks();

            for (Block block : allBlocks) {
                // Ensure chunk is loaded
                if (!block.getChunk().isLoaded()) {
                    block.getChunk().load();
                }

                Material type = block.getType();

                // Consider these materials as "empty" and needing reset
                if (type == Material.AIR ||
                    type == Material.WATER ||
                    type == Material.STATIONARY_WATER ||
                    type == Material.LAVA ||
                    type == Material.STATIONARY_LAVA) {

                    emptyBlocks.add(block.getLocation());
                }
            }


        } catch (Exception e) {
            // Fallback to manual iteration if cuboid.getBlocks() fails
           return findEmptyBlocksManually(cuboid);
        }

        return emptyBlocks;
    }

    /**
     * Fallback method for finding empty blocks manually
     */
    private List<Location> findEmptyBlocksManually(Cuboid cuboid) {
        List<Location> emptyBlocks = new ArrayList<>();

        World world = cuboid.getWorld();
        int minX = cuboid.getLowerX();
        int minY = cuboid.getLowerY();
        int minZ = cuboid.getLowerZ();
        int maxX = cuboid.getUpperX();
        int maxY = cuboid.getUpperY();
        int maxZ = cuboid.getUpperZ();

        int totalBlocks = 0;
        for (int x = minX; x <= maxX; x++) {
            for (int y = minY; y <= maxY; y++) {
                for (int z = minZ; z <= maxZ; z++) {
                    totalBlocks++;
                    Location location = new Location(world, x, y, z);

                    // Ensure chunk is loaded
                    if (!location.getChunk().isLoaded()) {
                        location.getChunk().load();
                    }

                    Block block = location.getBlock();
                    Material type = block.getType();

                    // Consider these materials as "empty" and needing reset
                    if (type == Material.AIR ||
                        type == Material.WATER ||
                        type == Material.STATIONARY_WATER ||
                        type == Material.LAVA ||
                        type == Material.STATIONARY_LAVA) {

                        emptyBlocks.add(location);
                    }
                }
            }
        }

        return emptyBlocks;
    }

    /**
     * Find which mine a location belongs to
     */
    private String findMineForLocation(Location location) {
        for (Map.Entry<String, Cuboid> entry : MINE_CUBOIDS.entrySet()) {
            if (entry.getValue().contains(location)) {
                return entry.getKey();
            }
        }
        return "stone"; // Default mine type
    }

    @Override
    public void run() {
        seconds--;

        // Multiple warnings for better player preparation
        if (seconds == 60) {
            Bukkit.broadcastMessage("§e§lAs minas vão resetar em 1 minuto!");
        } else if (seconds == 30) {
            Bukkit.broadcastMessage("§6§lAs minas vão resetar em 30 segundos!");
        } else if (seconds == 10) {
            Bukkit.broadcastMessage("§c§lAs minas vão resetar em 10 segundos!");
        }

        if (seconds == 0) {
            // Always reset mines - we'll find empty blocks during the reset process
            resetMines();
            seconds = RESET_INTERVAL_SECONDS;
        }
    }

    /**
     * Manually trigger mine reset (for admin commands)
     */
    public static void forceReset() {
        Bukkit.broadcastMessage("§c§lReset manual das minas iniciado!");
        new Minas().resetMines();
    }

    /**
     * Get time until next reset
     */
    public int getTimeUntilReset() {
        return seconds;
    }

    /**
     * Get formatted time until next reset
     */
    public static String getFormattedTimeUntilReset(int seconds) {
        if (seconds >= 60) {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            return minutes + "m " + remainingSeconds + "s";
        } else {
            return seconds + "s";
        }
    }

    /**
     * Check if a material requires physics updates
     */
    public static boolean requiresPhysics(Material material) {
        return PHYSICS_MATERIALS.contains(material);
    }

    /**
     * Add a custom physics material (for configuration)
     */
    public static void addPhysicsMaterial(Material material) {
        PHYSICS_MATERIALS.add(material);
    }

    /**
     * Remove a physics material (for configuration)
     */
    public static void removePhysicsMaterial(Material material) {
        PHYSICS_MATERIALS.remove(material);
    }

    /**
     * Get current queue sizes (for debugging)
     */
    public static String getQueueStatus() {
        return "Blocks: " + blockResetQueue.size();
    }

    /**
     * Add or update a mine composition
     */
    public static void setMineComposition(String mineName, MineComposition composition) {
        MINE_COMPOSITIONS.put(mineName, composition);
    }

    /**
     * Get a mine composition
     */
    public static MineComposition getMineComposition(String mineName) {
        return MINE_COMPOSITIONS.get(mineName);
    }

    /**
     * Get all mine names
     */
    public static Set<String> getMineNames() {
        return new HashSet<>(MINE_CUBOIDS.keySet());
    }

    /**
     * Get all composition names
     */
    public static Set<String> getCompositionNames() {
        return new HashSet<>(MINE_COMPOSITIONS.keySet());
    }

    /**
     * Get mine information for debugging
     */
    public static String getMineInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§a§lMine Information:\n");

        for (String mineName : MINE_CUBOIDS.keySet()) {
            MineComposition composition = MINE_COMPOSITIONS.get(mineName);
            info.append("§7Mine: §f").append(mineName);

            if (composition != null) {
                info.append(" §7(").append(composition.getBlocks().size()).append(" block types)\n");
                for (WeightedBlock block : composition.getBlocks()) {
                    double percentage = block.getPercentage(composition.getTotalWeight());
                    info.append("  §7- §f").append(block.material.name())
                        .append(" §7(").append(String.format("%.1f", percentage)).append("%)\n");
                }
            } else {
                info.append(" §c(No composition defined)\n");
            }
        }

        return info.toString();
    }

    /**
     * Represents a mine composition with weighted block types
     */
    public static class MineComposition {
        private final List<WeightedBlock> blocks = new ArrayList<>();
        private double totalWeight = 0.0;

        /**
         * Add a block type with its weight (percentage)
         */
        public void addBlock(Material material, double weight) {
            blocks.add(new WeightedBlock(material, weight));
            totalWeight += weight;
        }

        /**
         * Add a block type with data value and weight (percentage)
         */
        public void addBlock(Material material, byte data, double weight) {
            blocks.add(new WeightedBlock(material, data, weight));
            totalWeight += weight;
        }

        /**
         * Get a random block based on the composition weights
         */
        public WeightedBlock getRandomWeightedBlock() {
            if (blocks.isEmpty()) {
                return new WeightedBlock(Material.STONE, 100.0); // Default fallback
            }

            double randomValue = RANDOM.nextDouble() * totalWeight;
            double currentWeight = 0.0;

            for (WeightedBlock block : blocks) {
                currentWeight += block.weight;
                if (randomValue <= currentWeight) {
                    return block;
                }
            }

            // Fallback to last block if something goes wrong
            return blocks.get(blocks.size() - 1);
        }

        /**
         * Get a random block material (legacy method)
         */
        public Material getRandomBlock() {
            return getRandomWeightedBlock().material;
        }

        /**
         * Get all blocks in this composition
         */
        public List<WeightedBlock> getBlocks() {
            return new ArrayList<>(blocks);
        }

        /**
         * Get total weight
         */
        public double getTotalWeight() {
            return totalWeight;
        }
    }

    /**
     * Represents a weighted block in a mine composition
     */
    public static class WeightedBlock {
        public final Material material;
        public final byte data;
        public final double weight;

        public WeightedBlock(Material material, double weight) {
            this.material = material;
            this.data = 0; // Default data value
            this.weight = weight;
        }

        public WeightedBlock(Material material, byte data, double weight) {
            this.material = material;
            this.data = data;
            this.weight = weight;
        }

        public double getPercentage(double totalWeight) {
            return (weight / totalWeight) * 100.0;
        }

        public String getDisplayName() {
            switch (material) {
                case LOG:
                    switch (data) {
                        case 0: return "Oak Wood";
                        case 1: return "Spruce Wood";
                        case 2: return "Birch Wood";
                        case 3: return "Jungle Wood";
                        default: return "Wood";
                    }
                case LOG_2:
                    switch (data) {
                        case 0: return "Acacia Wood";
                        case 1: return "Dark Oak Wood";
                        default: return "Wood";
                    }
                case SAND:
                    switch (data) {
                        case 0: return "Sand";
                        case 1: return "Red Sand";
                        default: return "Sand";
                    }
                case STONE:
                    switch (data) {
                        case 0: return "Stone";
                        case 1: return "Granite";
                        case 2: return "Polished Granite";
                        case 3: return "Diorite";
                        case 4: return "Polished Diorite";
                        case 5: return "Andesite";
                        case 6: return "Polished Andesite";
                        default: return "Stone";
                    }
                default:
                    return material.name().replace("_", " ");
            }
        }

        @Override
        public String toString() {
            return getDisplayName() + " (" + String.format("%.1f", weight) + "%)";
        }
    }

    /**
     * Legacy BlockInfo class (kept for compatibility)
     */
    public static class BlockInfo {
        public final Material type;
        public final byte data;

        public BlockInfo(Material type, byte data) {
            this.type = type;
            this.data = data;
        }

        @Override
        public String toString() {
            return type.name() + ":" + data;
        }
    }
}