package me.miguel19877.dev.Tasks;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.TitleAPI;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class Evento extends BukkitRunnable {

    public static BukkitTask start(int delay, int period) {
        return new Evento().runTaskTimer(Rankup.getInstance(), delay, period);
    }

    public static int getSeconds() {
        return seconds;
    }

    public static void setSeconds() {
        seconds = 3600;
    }

    private static int seconds = 0;

    @Override
    public void run() {
        seconds--;
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm");
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("HH:mm:ss");
        if (seconds <= 30 && seconds > 1) {
            Bukkit.broadcastMessage("§c§lO Evento irá acabar em §6§l" + seconds + " §c§lsegundos!");
        }
        if (seconds <= 10 && seconds >= 1) {
            for (Player p : Bukkit.getOnlinePlayers()) {
                TitleAPI.sendTitle(p, 5, 10, 5, "§c§l" + seconds, "");
            }
        }
        if (seconds == 1) {
            Bukkit.broadcastMessage("§c§lO Evento irá acabar em §6§l" + seconds + " §c§lsegundo!");
        }

        if(seconds == 0) {
            Bukkit.broadcastMessage("§c§lO Evento acabou!");
            Bukkit.broadcastMessage("§c§lO Evento acabou!");
            Bukkit.broadcastMessage("§c§lO Evento acabou!");
            for (Player p : Bukkit.getOnlinePlayers()) {
                TitleAPI.sendTitle(p, 10, 80, 10, "§6§lEVENTO", "§c§lO Evento acabou!");
            }
            cancel();
        }
    }
}
