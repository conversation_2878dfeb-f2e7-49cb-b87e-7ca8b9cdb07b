package me.miguel19877.dev.Tasks;

import me.miguel19877.dev.managers.StreamRewardManager;
import me.miguel19877.dev.managers.TwitchManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.Socket;
import java.util.Iterator;

public class LivestreamTrack extends BukkitRunnable {

    @Override
    public void run() {
        if (TwitchManager.getChannelLive() != null && !TwitchManager.getChannelLive().isEmpty()) {
            Iterator<String> iterator = TwitchManager.getChannelLive().iterator();
            while (iterator.hasNext()) {
                String streamer = iterator.next();
                try {
                    boolean isLive = false;
                    JSONObject jsonObject = new JSONObject();
                    try (Socket socket = new Socket("localhost", 65432);
                         BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {
                        jsonObject.put("type", "checkLive");
                        jsonObject.put("channel", streamer);
                        socket.getOutputStream().write(jsonObject.toString().getBytes());
                        String response = "";
                        while (!response.equals("fff")) {
                            response = in.readLine();
                            if (response != null && !response.contains("\"stream\":null")) {
                                isLive = true;
                            }
                            response = "fff";
                        }
                    }
                    if (!isLive) {
                        iterator.remove();
                        TwitchManager.removeChannelLive(streamer);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            getViewers();
        }
    }

    public void getViewers() {
        JSONObject jsonObject = new JSONObject();
        try {
            // Connect to socket on port 12345 and send a JSONObject with the type "getViewers", it will return a string which is a JSONObject
            // with the viewers of the channel

            Socket socket = new Socket("localhost", 12345);
            DataOutputStream out = new DataOutputStream(socket.getOutputStream());
            DataInputStream din=new DataInputStream(socket.getInputStream());
            jsonObject.put("type", "getViewers");
            out.writeUTF(jsonObject.toString());
            out.flush();
            String line = "";
            while (!line.equals("disconnect")) {
                String stuff = din.readUTF();
                System.out.println(stuff);
                jsonObject = new JSONObject(stuff);
                line = "disconnect";
            }
            socket.close();
            din.close();
            out.close();
        } catch (Exception e) {
            System.err.println("Error getting viewers from socket: " + e.getMessage());
            e.printStackTrace();
            return; // Exit early if we can't get viewer data
        }

        // Parse viewers by channel - the JSON response contains viewers grouped by channel
        // Each key is a channel name, and the value is a JSON array of viewer usernames

        // First, let's process each online player
        for (Player player : Bukkit.getOnlinePlayers()) {
            String playerTwitchName = null;

            // Get the player's Twitch username if they have one linked
            if (TwitchManager.checkPlayerTwitch(player.getUniqueId())) {
                playerTwitchName = TwitchManager.getTwitchChannel(player.getUniqueId());
            }

            // Skip players without linked Twitch accounts
            if (playerTwitchName == null) {
                StreamRewardManager.stopAllTracking(player);
                continue;
            }

            boolean isWatchingAnyStreamer = false;

            // Check each channel in the JSON response to see if this player is watching
            for (String channelName : jsonObject.keySet()) {
                try {
                    JSONArray viewersArray = jsonObject.getJSONArray(channelName);

                    // Check if this player's Twitch username is in the viewers list for this channel
                    boolean isWatchingThisChannel = false;
                    for (int i = 0; i < viewersArray.length(); i++) {
                        String viewerName = viewersArray.getString(i);
                        if (playerTwitchName.equalsIgnoreCase(viewerName)) {
                            isWatchingThisChannel = true;
                            break;
                        }
                    }

                    if (isWatchingThisChannel) {
                        // Skip if the player is watching their own channel (can't get rewards for watching yourself)
                            /*if (playerTwitchName.equalsIgnoreCase(channelName)) {
                                continue;
                            }*/

                        // Check if this channel is in our live streamers list AND verify they're actually live
                        // Use case-insensitive comparison for channel names
                        boolean channelIsInLiveList = false;
                        String matchedLiveChannel = null;
                        for (String liveChannel : TwitchManager.getChannelLive()) {
                            if (liveChannel.equalsIgnoreCase(channelName)) {
                                channelIsInLiveList = true;
                                matchedLiveChannel = liveChannel;
                                break;
                            }
                        }

                        if (channelIsInLiveList) {
                            boolean streamerIsLive = isStreamerLive(matchedLiveChannel); // Use the matched channel name

                            if (streamerIsLive) {
                                // Track watch time and handle rewards for this streamer (use the matched channel name)
                                StreamRewardManager.trackWatchTime(player, matchedLiveChannel);
                                isWatchingAnyStreamer = true;
                                // Optional: Notify player they're watching a live streamer
                                // player.sendMessage("§aEstas a visualizar " + matchedLiveChannel + " que está live!");
                            } else {
                                // Streamer is not actually live, stop tracking this specific streamer
                                StreamRewardManager.stopTracking(player, matchedLiveChannel);
                            }
                        } else {
                            // Channel is not in our live list, stop tracking
                            StreamRewardManager.stopTracking(player, channelName);
                        }
                    }
                } catch (Exception e) {
                    // Skip this channel if there's an error parsing the JSON
                    continue;
                }
            }

            // If player is not watching any live streamer, stop all tracking for this player
            if (!isWatchingAnyStreamer) {
                StreamRewardManager.stopAllTracking(player);
            }
        }
    }

    /**
     * Check if a specific streamer is currently live
     * @param streamerChannel The Twitch channel to check
     * @return true if the streamer is live, false otherwise
     */
    private boolean isStreamerLive(String streamerChannel) {
        try {
            JSONObject jsonObject = new JSONObject();
            try (Socket socket = new Socket("localhost", 65432);
                 BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

                jsonObject.put("type", "checkLive");
                jsonObject.put("channel", streamerChannel);
                socket.getOutputStream().write(jsonObject.toString().getBytes());

                String response = "";
                while (!response.equals("fff")) {
                    response = in.readLine();
                    if (response != null && !response.contains("\"stream\":null")) {
                        return true; // Streamer is live
                    }
                    response = "fff";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false; // Streamer is not live or error occurred
    }
}
