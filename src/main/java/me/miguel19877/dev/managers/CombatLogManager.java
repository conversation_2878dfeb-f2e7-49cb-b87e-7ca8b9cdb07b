package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CombatLogManager {
    
    private static CombatLogManager instance;
    private final Map<UUID, Long> combatLoggedPlayers = new HashMap<>();
    private final Map<UUID, BukkitTask> combatTasks = new HashMap<>();
    private final long COMBAT_TIME = 5000; // 5 seconds in milliseconds
    
    private CombatLogManager() {}
    
    public static CombatLogManager getInstance() {
        if (instance == null) {
            instance = new CombatLogManager();
        }
        return instance;
    }
    
    /**
     * Put a player in combat log
     * @param player The player to put in combat
     */
    public void putInCombat(Player player) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Cancel existing task if player is already in combat
        BukkitTask existingTask = combatTasks.get(playerId);
        if (existingTask != null) {
            existingTask.cancel();
        }
        
        // Update combat time
        combatLoggedPlayers.put(playerId, currentTime);
        
        // Send combat message
        String combatMessage = LanguageManager.getInstance().getMessage(player, "combat.entered");
        if (combatMessage == null) {
            combatMessage = "§c§lCOMBATE! §7Não podes sair do servidor nem usar comandos por 5 segundos!";
        }
        player.sendMessage(combatMessage);
        
        // Create new task to remove from combat after 5 seconds
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                removeFromCombat(playerId);
            }
        }.runTaskLater(Rankup.getInstance(), 100L); // 5 seconds = 100 ticks
        
        combatTasks.put(playerId, task);
    }
    
    /**
     * Remove a player from combat log
     * @param playerId The UUID of the player to remove from combat
     */
    public void removeFromCombat(UUID playerId) {
        combatLoggedPlayers.remove(playerId);
        
        BukkitTask task = combatTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
        
        // Send combat end message if player is online
        Player player = Bukkit.getPlayer(playerId);
        if (player != null && player.isOnline()) {
            String combatEndMessage = LanguageManager.getInstance().getMessage(player, "combat.ended");
            if (combatEndMessage == null) {
                combatEndMessage = "§a§lCOMBATE TERMINADO! §7Já podes usar comandos e sair do servidor.";
            }
            player.sendMessage(combatEndMessage);
        }
    }
    
    /**
     * Check if a player is in combat
     * @param player The player to check
     * @return true if the player is in combat, false otherwise
     */
    public boolean isInCombat(Player player) {
        return isInCombat(player.getUniqueId());
    }
    
    /**
     * Check if a player is in combat by UUID
     * @param playerId The UUID of the player to check
     * @return true if the player is in combat, false otherwise
     */
    public boolean isInCombat(UUID playerId) {
        Long combatTime = combatLoggedPlayers.get(playerId);
        if (combatTime == null) {
            return false;
        }
        
        // Check if combat time has expired (safety check)
        long currentTime = System.currentTimeMillis();
        if (currentTime - combatTime > COMBAT_TIME) {
            removeFromCombat(playerId);
            return false;
        }
        
        return true;
    }
    
    /**
     * Get remaining combat time for a player in seconds
     * @param player The player to check
     * @return remaining combat time in seconds, or 0 if not in combat
     */
    public int getRemainingCombatTime(Player player) {
        Long combatTime = combatLoggedPlayers.get(player.getUniqueId());
        if (combatTime == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - combatTime;
        long remaining = COMBAT_TIME - elapsed;
        
        if (remaining <= 0) {
            removeFromCombat(player.getUniqueId());
            return 0;
        }
        
        return (int) Math.ceil(remaining / 1000.0);
    }
    
    /**
     * Handle player quit while in combat
     * This will drop all inventory and clear the player
     * @param player The player who quit
     */
    public void handleCombatQuit(Player player) {
        if (!isInCombat(player)) {
            return;
        }
        
        // Drop all inventory items at player location
        ItemStack[] inventory = player.getInventory().getContents().clone();
        ItemStack[] armor = player.getInventory().getArmorContents().clone();
        // Clear player inventory
        player.getInventory().clear();
        player.getInventory().setArmorContents(new ItemStack[4]);
        
        for (ItemStack item : inventory) {
            if (item != null && item.getType() != Material.AIR) {
                player.getWorld().dropItemNaturally(player.getLocation(), item);
            }
        }
        
        for (ItemStack item : armor) {
            if (item != null && item.getType() != Material.AIR) {
                player.getWorld().dropItemNaturally(player.getLocation(), item);
            }
        }
        
        // Remove from combat tracking
        removeFromCombat(player.getUniqueId());
        
        // Broadcast combat log message
        String quitMessage = LanguageManager.getInstance().getMessage(player, "combat.quit", player.getName());
        if (quitMessage == null) {
            quitMessage = "§c" + player.getName() + " §7saiu durante o combate e perdeu todos os seus itens!";
        }
        Bukkit.broadcastMessage(quitMessage);
    }
    
    /**
     * Clean up combat data for a player (called on quit)
     * @param playerId The UUID of the player
     */
    public void cleanupPlayer(UUID playerId) {
        BukkitTask task = combatTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
        combatLoggedPlayers.remove(playerId);
    }
    
    /**
     * Get all players currently in combat (for debugging/admin purposes)
     * @return Map of player UUIDs to combat start times
     */
    public Map<UUID, Long> getCombatLoggedPlayers() {
        return new HashMap<>(combatLoggedPlayers);
    }
}
