package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.PlayerDeltaManager;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.UUID;

public class PrestigeManager {

    private static final HashMap<UUID, Integer> prestigeLevels = new HashMap<>();
    private static final String PRESTIGE_KEY = "prestige:";

    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    public static int getPrestigeLevel(Player player) {
        return prestigeLevels.getOrDefault(player.getUniqueId(), 0);
    }

    public static void addPrestigeLevel(Player player) {
        int currentLevel = getPrestigeLevel(player);
        prestigeLevels.put(player.getUniqueId(), currentLevel + 1);
        savePlayer(player);
    }

    public static void setPrestigeLevel(Player player, int level) {
        prestigeLevels.put(player.getUniqueId(), level);
    }

    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String prestige = jedis.get(PRESTIGE_KEY + playerId.toString());
            prestigeLevels.put(playerId, prestige != null ? Integer.parseInt(prestige) : 0);
        }
    }

    public static void savePlayer(Player player) {
        Rankup.getInstance().deltaManager.recordPrestigeChange(player.getUniqueId(), getPrestigeLevel(player));
    }
}