package me.miguel19877.dev.managers;

import me.miguel19877.dev.managers.VoteKeyManager;
import org.bukkit.Bukkit;
import org.bukkit.event.Event;
import org.bukkit.plugin.java.JavaPlugin;

import javax.crypto.BadPaddingException;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.security.PrivateKey;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

public class VoteListener implements Runnable {

    private final JavaPlugin plugin;
    private final VoteKeyManager voteKeyManager;
    private final int port = 5023;
    private boolean running = true;
    private ServerSocket server;
    private static final Logger LOG = Logger.getLogger(VoteListener.class.getName());

    public VoteListener(JavaPlugin plugin, VoteKeyManager voteKeyManager) {
        this.plugin = plugin;
        this.voteKeyManager = voteKeyManager;
    }

    public void start() throws IOException {
        server = new ServerSocket(port);
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, this);
    }

    public void stop() {
        running = false;
        try {
            if (server != null) {
                server.close();
            }
        } catch (IOException e) {
            LOG.log(Level.SEVERE, "Error closing the server socket.", e);
        }
    }

    @Override
    public void run() {
        while (running) {
            try {
                Socket socket = server.accept();
                socket.setSoTimeout(5000);
                BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream()));
                InputStream in = socket.getInputStream();

                writer.write("VOTIFIER 1.0");
                writer.newLine();
                writer.flush();

                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                byte[] data = new byte[256];
                int bytesRead;

                // Read data into buffer
                while ((bytesRead = in.read(data, 0, data.length)) != -1) {
                    buffer.write(data, 0, bytesRead);
                }
                buffer.flush();
                byte[] block = buffer.toByteArray();

                // Decrypt the block
                block = voteKeyManager.decrypt(block); // Use VoteKeyManager to decrypt the block
                int position = 0;

                // Opcode will be the first line of the blockString
                //Get the entire block as string
                String blockString = new String(block);
                String opcode = blockString.substring(0, blockString.indexOf("\n"));
                Bukkit.getConsoleSender().sendMessage(blockString);
                if (!opcode.equals("VOTE")) {
                    throw new Exception("Unable to decode RSA");
                }

                // ServiceName will be the second line of the blockString
                String serviceName = blockString.substring(blockString.indexOf("\n") + 1, blockString.indexOf("\n", blockString.indexOf("\n") + 1));
                // Username will be the third line of the blockString
                String username = blockString.substring(blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1, blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1));
                // IP will be the fourth line of the blockString
                String ip = blockString.substring(blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1) + 1, blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1) + 1));
                // TimeStamp will be the fifth line of the blockString
                String timeStamp = blockString.substring(blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1) + 1) + 1, blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n", blockString.indexOf("\n") + 1) + 1) + 1) + 1));

                Bukkit.broadcastMessage("§6§lVoto §7§l» §a" + username + " §7acaba de votar no servidor e ganhou recompensas! /votar");


                writer.close();
                in.close();
                socket.close();
            } catch (SocketException ex) {
                LOG.log(Level.WARNING, "Protocol error. Ignoring packet - " + ex.getLocalizedMessage());
            } catch (BadPaddingException ex) {
                LOG.log(Level.WARNING, "Unable to decrypt vote record. Make sure that your public key matches the one you gave the server list.", ex);
            } catch (Exception ex) {
                LOG.log(Level.WARNING, "Exception caught while receiving a vote notification", ex);
            }
        }
    }

    private String readString(byte[] data, int offset) {
        StringBuilder sb = new StringBuilder();
        for (int i = offset; i < data.length && data[i] != 0; i++) {
            sb.append((char) data[i]);
        }
        return sb.toString();
    }
}
