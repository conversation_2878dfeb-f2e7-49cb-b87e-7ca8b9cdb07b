package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class TPAManager {
    
    private static TPAManager instance;
    private final Map<UUID, TPARequest> activeRequests = new HashMap<>();
    private final Map<UUID, BukkitTask> requestTasks = new HashMap<>();
    private final long REQUEST_TIMEOUT = 60000; // 60 seconds in milliseconds
    
    private TPAManager() {}
    
    public static TPAManager getInstance() {
        if (instance == null) {
            instance = new TPAManager();
        }
        return instance;
    }
    
    /**
     * Send a TPA request from one player to another
     * @param requester The player sending the request
     * @param target The player receiving the request
     * @return true if request was sent successfully, false if there's already a pending request
     */
    public boolean sendTPARequest(Player requester, Player target) {
        UUID targetId = target.getUniqueId();
        
        // Check if target already has a pending request
        if (activeRequests.containsKey(targetId)) {
            return false;
        }
        
        // Create new request
        TPARequest request = new TPARequest(requester.getUniqueId(), targetId, System.currentTimeMillis());
        activeRequests.put(targetId, request);
        
        // Send messages
        String requestSentMessage = LanguageManager.getInstance().getMessage(requester, "tpa.request_sent", target.getName());
        if (requestSentMessage == null) {
            requestSentMessage = "§aPedido de teleporte enviado para §6" + target.getName() + "§a!";
        }
        requester.sendMessage(requestSentMessage);
        
        String requestReceivedMessage = LanguageManager.getInstance().getMessage(target, "tpa.request_received", requester.getName());
        if (requestReceivedMessage == null) {
            requestReceivedMessage = "§6" + requester.getName() + " §7quer se teleportar para ti!";
        }
        target.sendMessage(requestReceivedMessage);
        
        String acceptMessage = LanguageManager.getInstance().getMessage(target, "tpa.accept_instructions");
        if (acceptMessage == null) {
            acceptMessage = "§7Use §a/tpaccept §7para aceitar ou §c/tpdeny §7para recusar.";
        }
        target.sendMessage(acceptMessage);
        
        // Set timeout task
        BukkitTask timeoutTask = new BukkitRunnable() {
            @Override
            public void run() {
                expireRequest(targetId);
            }
        }.runTaskLater(Rankup.getInstance(), 1200L); // 60 seconds = 1200 ticks
        
        requestTasks.put(targetId, timeoutTask);
        
        return true;
    }
    
    /**
     * Accept a TPA request
     * @param target The player accepting the request
     * @return true if request was accepted successfully, false if no pending request
     */
    public boolean acceptTPARequest(Player target) {
        UUID targetId = target.getUniqueId();
        TPARequest request = activeRequests.get(targetId);
        
        if (request == null) {
            return false;
        }
        
        Player requester = Bukkit.getPlayer(request.getRequesterId());
        if (requester == null || !requester.isOnline()) {
            // Requester is offline, remove request
            removeRequest(targetId);
            String offlineMessage = LanguageManager.getInstance().getMessage(target, "tpa.requester_offline");
            if (offlineMessage == null) {
                offlineMessage = "§cO jogador que fez o pedido não está mais online.";
            }
            target.sendMessage(offlineMessage);
            return false;
        }
        
        // Check if requester is in combat
        if (CombatLogManager.getInstance().isInCombat(requester)) {
            String combatMessage = LanguageManager.getInstance().getMessage(target, "tpa.requester_in_combat");
            if (combatMessage == null) {
                combatMessage = "§cO jogador está em combate e não pode ser teleportado!";
            }
            target.sendMessage(combatMessage);
            
            String combatMessageRequester = LanguageManager.getInstance().getMessage(requester, "tpa.cannot_teleport_combat");
            if (combatMessageRequester == null) {
                combatMessageRequester = "§cNão podes ser teleportado enquanto estás em combate!";
            }
            requester.sendMessage(combatMessageRequester);
            return false;
        }
        
        // Check if target is in combat
        if (CombatLogManager.getInstance().isInCombat(target)) {
            String combatMessage = LanguageManager.getInstance().getMessage(target, "tpa.target_in_combat");
            if (combatMessage == null) {
                combatMessage = "§cNão podes aceitar teleportes enquanto estás em combate!";
            }
            target.sendMessage(combatMessage);
            return false;
        }
        
        // Perform teleport
        requester.teleport(target.getLocation());
        
        // Send success messages
        String acceptedMessage = LanguageManager.getInstance().getMessage(target, "tpa.request_accepted", requester.getName());
        if (acceptedMessage == null) {
            acceptedMessage = "§aPedido de teleporte de §6" + requester.getName() + " §aaceito!";
        }
        target.sendMessage(acceptedMessage);
        
        String teleportedMessage = LanguageManager.getInstance().getMessage(requester, "tpa.teleported_to", target.getName());
        if (teleportedMessage == null) {
            teleportedMessage = "§aTeleportado para §6" + target.getName() + "§a!";
        }
        requester.sendMessage(teleportedMessage);
        
        // Remove request
        removeRequest(targetId);
        
        return true;
    }
    
    /**
     * Deny a TPA request
     * @param target The player denying the request
     * @return true if request was denied successfully, false if no pending request
     */
    public boolean denyTPARequest(Player target) {
        UUID targetId = target.getUniqueId();
        TPARequest request = activeRequests.get(targetId);
        
        if (request == null) {
            return false;
        }
        
        Player requester = Bukkit.getPlayer(request.getRequesterId());
        
        // Send messages
        String deniedMessage = LanguageManager.getInstance().getMessage(target, "tpa.request_denied");
        if (deniedMessage == null) {
            deniedMessage = "§cPedido de teleporte recusado.";
        }
        target.sendMessage(deniedMessage);
        
        if (requester != null && requester.isOnline()) {
            String deniedByMessage = LanguageManager.getInstance().getMessage(requester, "tpa.request_denied_by", target.getName());
            if (deniedByMessage == null) {
                deniedByMessage = "§c" + target.getName() + " recusou o teu pedido de teleporte.";
            }
            requester.sendMessage(deniedByMessage);
        }
        
        // Remove request
        removeRequest(targetId);
        
        return true;
    }
    
    /**
     * Check if a player has a pending TPA request
     * @param player The player to check
     * @return true if player has a pending request, false otherwise
     */
    public boolean hasPendingRequest(Player player) {
        return activeRequests.containsKey(player.getUniqueId());
    }
    
    /**
     * Get the remaining time for a TPA request in seconds
     * @param player The player to check
     * @return remaining time in seconds, or 0 if no pending request
     */
    public int getRemainingTime(Player player) {
        TPARequest request = activeRequests.get(player.getUniqueId());
        if (request == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - request.getTimestamp();
        long remaining = REQUEST_TIMEOUT - elapsed;
        
        if (remaining <= 0) {
            expireRequest(player.getUniqueId());
            return 0;
        }
        
        return (int) Math.ceil(remaining / 1000.0);
    }
    
    /**
     * Expire a TPA request due to timeout
     * @param targetId The UUID of the target player
     */
    private void expireRequest(UUID targetId) {
        TPARequest request = activeRequests.get(targetId);
        if (request == null) {
            return;
        }
        
        Player target = Bukkit.getPlayer(targetId);
        Player requester = Bukkit.getPlayer(request.getRequesterId());
        
        // Send timeout messages
        if (target != null && target.isOnline()) {
            String timeoutMessage = LanguageManager.getInstance().getMessage(target, "tpa.request_expired");
            if (timeoutMessage == null) {
                timeoutMessage = "§cO pedido de teleporte expirou.";
            }
            target.sendMessage(timeoutMessage);
        }
        
        if (requester != null && requester.isOnline()) {
            String timeoutMessage = LanguageManager.getInstance().getMessage(requester, "tpa.request_expired_sender");
            if (timeoutMessage == null) {
                timeoutMessage = "§cO teu pedido de teleporte expirou.";
            }
            requester.sendMessage(timeoutMessage);
        }
        
        // Remove request
        removeRequest(targetId);
    }
    
    /**
     * Remove a TPA request and clean up associated tasks
     * @param targetId The UUID of the target player
     */
    private void removeRequest(UUID targetId) {
        activeRequests.remove(targetId);
        
        BukkitTask task = requestTasks.remove(targetId);
        if (task != null) {
            task.cancel();
        }
    }
    
    /**
     * Clean up all requests for a player (called on quit)
     * @param playerId The UUID of the player
     */
    public void cleanupPlayer(UUID playerId) {
        // Remove any request where this player is the target
        removeRequest(playerId);
        
        // Remove any request where this player is the requester
        activeRequests.entrySet().removeIf(entry -> {
            if (entry.getValue().getRequesterId().equals(playerId)) {
                BukkitTask task = requestTasks.remove(entry.getKey());
                if (task != null) {
                    task.cancel();
                }
                return true;
            }
            return false;
        });
    }
    
    /**
     * Inner class to represent a TPA request
     */
    private static class TPARequest {
        private final UUID requesterId;
        private final UUID targetId;
        private final long timestamp;
        
        public TPARequest(UUID requesterId, UUID targetId, long timestamp) {
            this.requesterId = requesterId;
            this.targetId = targetId;
            this.timestamp = timestamp;
        }
        
        public UUID getRequesterId() {
            return requesterId;
        }
        
        public UUID getTargetId() {
            return targetId;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }
}
