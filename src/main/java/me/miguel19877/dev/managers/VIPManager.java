package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.UUID;

public class VIPManager {
    private static final String VIP_CODE_KEY_PREFIX = "vip_code:";
    private static final String VIP_TIME_KEY_PREFIX = "vip_time:";
    private static final HashMap<String, String> vipCodes = new HashMap<>();

    public VIPManager() {
        // Load any existing VIP codes from Redis into the vipCodes HashMap
        try (Jedis jedis = getJedis()) {
            String keysPattern = VIP_CODE_KEY_PREFIX + "*";
            for (String key : jedis.keys(keysPattern)) {
                String vipType = jedis.get(key);
                vipCodes.put(key.replace(VIP_CODE_KEY_PREFIX, ""), vipType);
            }
        }
    }

    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }


    public String verifyCode(String code) {
        if (vipCodes.containsKey(code)) {
            String vipType = vipCodes.get(code);
            removeCode(code);
            return vipType;
        }
        return "";
    }

    public void addCode(String code, String vipType) {
        try (Jedis jedis = getJedis()) {
            jedis.set(VIP_CODE_KEY_PREFIX + code, vipType);
        }
        vipCodes.put(code, vipType);
    }

    public void removeCode(String code) {
        vipCodes.remove(code);
        try (Jedis jedis = getJedis()) {
            jedis.del(VIP_CODE_KEY_PREFIX + code);
        }
    }

    public static void setTime(UUID uuid, long time) {
        try (Jedis jedis = getJedis()) {
            jedis.set(VIP_TIME_KEY_PREFIX + uuid.toString(), String.valueOf(time));
        }
        Rankup.getInstance().deltaManager.recordVipExpirationChange(uuid, time);
    }

    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String time = jedis.get(VIP_TIME_KEY_PREFIX + playerId.toString());
            if (time != null) {
                long timeLeft = Long.parseLong(time);
                if (timeLeft > 0 && (!(timeLeft > System.currentTimeMillis()))) {
                    setTime(playerId, 17291093043333L);
                    Permissions.setGrupo(player, 1);
                    Permissions.setGrupoOffline(Bukkit.getOfflinePlayer(player.getUniqueId()), 1);
                    Bukkit.getScheduler().runTaskLater(Rankup.getInstance(), () -> {
                        player.kickPlayer("§r§aVIP expirou!§r\n§r§bFoste kickado para as permissoes ficarem atualizadas§r");
                    }, 40L); // 10 ticks = 500ms

                }
            }
        }
    }

}