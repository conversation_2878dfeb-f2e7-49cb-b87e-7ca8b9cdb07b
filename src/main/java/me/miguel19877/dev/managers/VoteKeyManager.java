package me.miguel19877.dev.managers;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;
import org.bukkit.plugin.java.JavaPlugin;

public class VoteKeyManager {

    private final JavaPlugin plugin;
    public static PrivateKey privateKey;
    private VoteListener voteListenerManager;
    private final int votifierPort = 5023; // Example port, change as needed

    public VoteKeyManager(JavaPlugin plugin) {
        this.plugin = plugin;
    }

    public void generateKeys() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(2048);
        KeyPair pair = keyGen.generateKeyPair();

        PublicKey publicKey = pair.getPublic();
        privateKey = pair.getPrivate();

        File publicKeyFile = new File(plugin.getDataFolder(), "public.key");
        if (!publicKeyFile.exists()) {
            publicKeyFile.getParentFile().mkdirs();
            publicKeyFile.createNewFile();
        }
        try (FileOutputStream fos = new FileOutputStream(publicKeyFile)) {
            fos.write(Base64.getEncoder().encode(publicKey.getEncoded()));
        }

        File privateKeyFile = new File(plugin.getDataFolder(), "private.key");
        if (!privateKeyFile.exists()) {
            privateKeyFile.getParentFile().mkdirs();
            privateKeyFile.createNewFile();
        }
        try (FileOutputStream fos = new FileOutputStream(privateKeyFile)) {
            fos.write(Base64.getEncoder().encode(privateKey.getEncoded()));
        }
    }

    public void load() throws Exception {
        File privateKeyFile = new File(plugin.getDataFolder(), "private.key");
        if (!privateKeyFile.exists()) {
            generateKeys();
        } else {
            byte[] keyBytes = Files.readAllBytes(privateKeyFile.toPath());
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyBytes));
            KeyFactory kf = KeyFactory.getInstance("RSA");
            privateKey = kf.generatePrivate(spec);
        }

        voteListenerManager = new VoteListener(plugin, this);
        voteListenerManager.start();
    }

    public static byte[] decrypt(byte[] data) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher.doFinal(data);
    }

    public void stopListener() {
        if (voteListenerManager != null) {
            voteListenerManager.stop();
        }
    }
}

