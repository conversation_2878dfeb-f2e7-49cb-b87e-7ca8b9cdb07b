package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.PlayerDeltaManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;

import java.util.*;

public class BlocksMinedManager {
    // In-memory cache
    private static final Map<UUID, Long> playerBlocksMined = new HashMap<>();

    // Redis key prefixes
    private static final String PLAYER_BLOCKSMINED_KEY = "player:blocksmined:";

    // Utility method to get Jedis connection
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    // Load clan data from Redis to cache
    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String blocksMined = jedis.get(PLAYER_BLOCKSMINED_KEY + playerId);
            if (blocksMined != null) {
                playerBlocksMined.put(playerId, Long.parseLong(blocksMined));
            }else {
                playerBlocksMined.put(playerId, 0L);
            }
        }
    }

    // Save player clan data from cache to Redis
    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            Long blocksMined = playerBlocksMined.get(playerId);
            if (blocksMined != null) {
                jedis.set(PLAYER_BLOCKSMINED_KEY + playerId, blocksMined.toString());
            }
        }
    }

    public static void addBlocksMined(Player player, long blocksMined) {
        UUID playerId = player.getUniqueId();
        Long currentBlocksMined = playerBlocksMined.get(playerId);
        if (currentBlocksMined == null) {
            currentBlocksMined = 0L;
        }
        playerBlocksMined.put(playerId, currentBlocksMined + blocksMined);
        Rankup.getInstance().deltaManager.recordBlocksminedChange(player.getUniqueId(), currentBlocksMined + blocksMined);
    }

    public static long getBlocksMined(Player player) {
        UUID playerId = player.getUniqueId();
        Long blocksMined = playerBlocksMined.get(playerId);
        if (blocksMined == null) {
            return 0;
        }
        return blocksMined;
    }

    public static void setBlocksMined(Player player, long blocksMined) {
        UUID playerId = player.getUniqueId();
        playerBlocksMined.put(playerId, blocksMined);
    }
}
