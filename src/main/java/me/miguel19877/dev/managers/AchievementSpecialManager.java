package me.miguel19877.dev.managers;

import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Manages special achievements like mine visits, tool usage, enchantments, etc.
 */
public class AchievementSpecialManager {
    
    private static AchievementSpecialManager instance;
    
    // Track mine visits
    private final Map<UUID, Set<String>> visitedMines = new HashMap<>();
    
    // Track anvil usage
    private final Map<UUID, Integer> anvilUsage = new HashMap<>();
    
    // Track tool breaking
    private final Map<UUID, Integer> brokenTools = new HashMap<>();
    
    // Track enchanted pickaxe usage
    private final Map<UUID, Boolean> hasUsedEfficiencyV = new HashMap<>();
    
    // Track plot creation
    private final Map<UUID, Boolean> hasCreatedPlot = new HashMap<>();
    
    // Track plot permissions
    private final Map<UUID, Boolean> hasGivenPlotPermit = new HashMap<>();
    
    // Track event participation
    private final Map<UUID, Integer> eventParticipation = new HashMap<>();
    
    // Track leaderboard positions
    private final Map<UUID, Integer> leaderboardDays = new HashMap<>();
    
    // Track PvP death streaks (for inquebravel achievement)
    private final Map<UUID, Long> lastPvPDeath = new HashMap<>();
    private final Map<UUID, Integer> pvpDeathFreeDays = new HashMap<>();
    
    private AchievementSpecialManager() {}
    
    public static AchievementSpecialManager getInstance() {
        if (instance == null) {
            instance = new AchievementSpecialManager();
        }
        return instance;
    }
    
    /**
     * Called when a player visits a mine
     */
    public void onMineVisit(Player player, String mineName) {
        UUID playerId = player.getUniqueId();
        Set<String> playerMines = visitedMines.computeIfAbsent(playerId, k -> new HashSet<>());
        
        if (!playerMines.contains(mineName)) {
            playerMines.add(mineName);
            
            // explorador_local - Visit 3 different mines
            if (playerMines.size() >= 3) {
                AchievementHelper.setProgress(player, "explorador_local", playerMines.size());
            }
        }
    }
    
    /**
     * Called when a player uses an anvil
     */
    public void onAnvilUse(Player player) {
        UUID playerId = player.getUniqueId();
        int usage = anvilUsage.getOrDefault(playerId, 0) + 1;
        anvilUsage.put(playerId, usage);
        
        // engenharia_manual - Use anvil 25 times
        if (usage >= 25) {
            AchievementHelper.setProgress(player, "engenharia_manual", usage);
        }
    }
    
    /**
     * Called when a player breaks a tool
     */
    public void onToolBreak(Player player, ItemStack brokenTool) {
        UUID playerId = player.getUniqueId();
        
        // ferramenta_partida - Break a stone pickaxe
        if (brokenTool.getType() == Material.STONE_PICKAXE) {
            AchievementHelper.setProgress(player, "ferramenta_partida", 1);
        }
        
        // senhor_da_picareta - Use completely 5 enchanted pickaxes of high level
        if (isEnchantedPickaxe(brokenTool)) {
            int brokenCount = brokenTools.getOrDefault(playerId, 0) + 1;
            brokenTools.put(playerId, brokenCount);
            
            if (brokenCount >= 5) {
                AchievementHelper.setProgress(player, "senhor_da_picareta", brokenCount);
            }
        }
    }
    
    /**
     * Called when a player uses an Efficiency V pickaxe
     */
    public void onEfficiencyVUse(Player player) {
        UUID playerId = player.getUniqueId();
        if (!hasUsedEfficiencyV.getOrDefault(playerId, false)) {
            hasUsedEfficiencyV.put(playerId, true);
            AchievementHelper.setProgress(player, "forca_bruta", 1);
        }
    }
    
    /**
     * Called when a player creates their first plot
     */
    public void onPlotCreate(Player player) {
        UUID playerId = player.getUniqueId();
        if (!hasCreatedPlot.getOrDefault(playerId, false)) {
            hasCreatedPlot.put(playerId, true);
            AchievementHelper.setProgress(player, "construtor_iniciado", 1);
        }
    }
    
    /**
     * Called when a player gives plot permission to someone
     */
    public void onPlotPermitGiven(Player player) {
        UUID playerId = player.getUniqueId();
        if (!hasGivenPlotPermit.getOrDefault(playerId, false)) {
            hasGivenPlotPermit.put(playerId, true);
            AchievementHelper.setProgress(player, "tatica_de_grupo", 1);
        }
    }
    
    /**
     * Called when a player participates in an official event
     */
    public void onEventParticipation(Player player) {
        UUID playerId = player.getUniqueId();
        int participation = eventParticipation.getOrDefault(playerId, 0) + 1;
        eventParticipation.put(playerId, participation);
        
        // cacador_de_eventos - Participate in 25 official events
        if (participation >= 25) {
            AchievementHelper.setProgress(player, "cacador_de_eventos", participation);
        }
    }
    
    /**
     * Called when checking if player is in top 10 of any leaderboard
     */
    public void onLeaderboardCheck(Player player, boolean isInTop10) {
        UUID playerId = player.getUniqueId();
        
        if (isInTop10) {
            // top_10_mineracao - Enter top 10 weekly blocks broken
            AchievementHelper.setProgress(player, "top_10_mineracao", 1);
            
            // top_dos_tops - Enter top 10 of economy, mining, or PvP
            AchievementHelper.setProgress(player, "top_dos_tops", 1);
            
            // Track consecutive days in top 3 for nome_gravado_em_pedra
            int days = leaderboardDays.getOrDefault(playerId, 0) + 1;
            leaderboardDays.put(playerId, days);
            
            if (days >= 7) {
                AchievementHelper.setProgress(player, "nome_gravado_em_pedra", days);
            }
        } else {
            // Reset consecutive days if not in top 10
            leaderboardDays.put(playerId, 0);
        }
    }
    
    /**
     * Called when a player dies in PvP
     */
    public void onPvPDeath(Player player) {
        UUID playerId = player.getUniqueId();
        lastPvPDeath.put(playerId, System.currentTimeMillis());
        pvpDeathFreeDays.put(playerId, 0); // Reset death-free streak
    }
    
    /**
     * Check PvP death-free streak (should be called daily)
     */
    public void checkPvPDeathFreeStreak(Player player) {
        UUID playerId = player.getUniqueId();
        Long lastDeath = lastPvPDeath.get(playerId);
        
        if (lastDeath == null) {
            // Never died in PvP, start counting
            int days = pvpDeathFreeDays.getOrDefault(playerId, 0) + 1;
            pvpDeathFreeDays.put(playerId, days);
        } else {
            long daysSinceLastDeath = (System.currentTimeMillis() - lastDeath) / (1000 * 60 * 60 * 24);
            if (daysSinceLastDeath >= 1) {
                int days = pvpDeathFreeDays.getOrDefault(playerId, 0) + 1;
                pvpDeathFreeDays.put(playerId, days);
                
                // inquebravel - Don't die for 30 consecutive days in PvP zones
                if (days >= 30) {
                    AchievementHelper.setProgress(player, "inquebravel", days);
                }
            }
        }
    }
    
    /**
     * Check if an item is an enchanted pickaxe of high level
     */
    private boolean isEnchantedPickaxe(ItemStack item) {
        if (item == null || !isPickaxe(item.getType())) {
            return false;
        }
        
        return item.getEnchantments().size() > 0 && 
               (item.getType() == Material.DIAMOND_PICKAXE || item.getType() == Material.IRON_PICKAXE);
    }
    
    /**
     * Check if material is a pickaxe
     */
    private boolean isPickaxe(Material material) {
        switch (material) {
            case WOOD_PICKAXE:
            case STONE_PICKAXE:
            case IRON_PICKAXE:
            case GOLD_PICKAXE:
            case DIAMOND_PICKAXE:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * Clean up player data when they leave
     */
    public void cleanupPlayer(UUID playerId) {
        // Keep most data for when they return
        // Only clean up temporary session data if needed
    }
}
