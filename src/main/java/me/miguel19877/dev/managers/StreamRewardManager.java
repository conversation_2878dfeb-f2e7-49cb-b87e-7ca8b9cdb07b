package me.miguel19877.dev.managers;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import redis.clients.jedis.Jedis;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class StreamRewardManager {
    
    // In-memory tracking of watch times (in seconds)
    private static final Map<UUID, Map<String, Long>> playerWatchTimes = new ConcurrentHashMap<>();
    private static final Map<UUID, Map<String, Long>> playerStartTimes = new ConcurrentHashMap<>();
    
    // Redis key prefixes
    private static final String WATCH_TIME_KEY = "stream_watch:";
    private static final String REWARD_CLAIMED_KEY = "stream_rewards:";
    
    // Reward thresholds in seconds
    private static final long REWARD_30_MIN = 30 * 60; // 30 minutes
    private static final long REWARD_1_HOUR = 60 * 60; // 1 hour
    private static final long REWARD_2_HOURS = 120 * 60; // 2 hours
    
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }
    
    /**
     * Start tracking watch time for a player watching a specific streamer
     */
    public static void trackWatchTime(Player player, String streamerChannel) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis() / 1000; // Convert to seconds

        // Initialize maps if they don't exist
        playerWatchTimes.computeIfAbsent(playerId, k -> new HashMap<>());
        playerStartTimes.computeIfAbsent(playerId, k -> new HashMap<>());

        // If this is the first time tracking this streamer, set start time
        if (!playerStartTimes.get(playerId).containsKey(streamerChannel)) {
            playerStartTimes.get(playerId).put(streamerChannel, currentTime);

            // Load existing watch time from Redis
            try (Jedis jedis = getJedis()) {
                String watchTimeStr = jedis.get(WATCH_TIME_KEY + playerId + ":" + streamerChannel);
                long existingWatchTime = watchTimeStr != null ? Long.parseLong(watchTimeStr) : 0;
                playerWatchTimes.get(playerId).put(streamerChannel, existingWatchTime);
            }
        }

        // Update watch time
        updateWatchTime(player, streamerChannel);
    }
    
    /**
     * Stop tracking for a specific streamer
     */
    public static void stopTracking(Player player, String streamerChannel) {
        UUID playerId = player.getUniqueId();
        
        if (playerStartTimes.containsKey(playerId) && 
            playerStartTimes.get(playerId).containsKey(streamerChannel)) {
            
            // Update final watch time before stopping
            updateWatchTime(player, streamerChannel);
            
            // Remove from tracking
            playerStartTimes.get(playerId).remove(streamerChannel);
            
            // Save to Redis
            saveWatchTime(player, streamerChannel);
        }
    }
    
    /**
     * Stop all tracking for a player
     */
    public static void stopAllTracking(Player player) {
        UUID playerId = player.getUniqueId();
        
        if (playerStartTimes.containsKey(playerId)) {
            // Update all watch times before stopping
            for (String streamer : playerStartTimes.get(playerId).keySet()) {
                updateWatchTime(player, streamer);
                saveWatchTime(player, streamer);
            }
            
            // Clear all tracking
            playerStartTimes.get(playerId).clear();
        }
    }
    
    /**
     * Update the watch time for a player-streamer pair
     */
    private static void updateWatchTime(Player player, String streamerChannel) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis() / 1000;

        if (playerStartTimes.containsKey(playerId) &&
            playerStartTimes.get(playerId).containsKey(streamerChannel)) {

            long startTime = playerStartTimes.get(playerId).get(streamerChannel);
            long sessionTime = currentTime - startTime;

            // Add session time to total watch time
            long currentWatchTime = playerWatchTimes.get(playerId).getOrDefault(streamerChannel, 0L);
            long newWatchTime = currentWatchTime + sessionTime;
            playerWatchTimes.get(playerId).put(streamerChannel, newWatchTime);

            // Reset start time for next session
            playerStartTimes.get(playerId).put(streamerChannel, currentTime);

            // Check for rewards
            checkAndGiveRewards(player, streamerChannel, currentWatchTime, newWatchTime);
        }
    }
    
    /**
     * Save watch time to Redis
     */
    private static void saveWatchTime(Player player, String streamerChannel) {
        UUID playerId = player.getUniqueId();
        
        if (playerWatchTimes.containsKey(playerId) && 
            playerWatchTimes.get(playerId).containsKey(streamerChannel)) {
            
            long watchTime = playerWatchTimes.get(playerId).get(streamerChannel);
            
            try (Jedis jedis = getJedis()) {
                jedis.set(WATCH_TIME_KEY + playerId + ":" + streamerChannel, String.valueOf(watchTime));
            }
        }
    }
    
    /**
     * Check if player has reached reward thresholds and give rewards
     */
    private static void checkAndGiveRewards(Player player, String streamerChannel, long oldWatchTime, long newWatchTime) {
        UUID playerId = player.getUniqueId();

        // Check 30-minute reward
        if (oldWatchTime < REWARD_30_MIN && newWatchTime >= REWARD_30_MIN) {
            if (!hasClaimedReward(playerId, streamerChannel, "30min")) {
                giveEnchantedBookReward(player, streamerChannel);
                markRewardClaimed(playerId, streamerChannel, "30min");
            }
        }

        // Check 1-hour reward
        if (oldWatchTime < REWARD_1_HOUR && newWatchTime >= REWARD_1_HOUR) {
            if (!hasClaimedReward(playerId, streamerChannel, "1hour")) {
                giveMoneyReward(player, streamerChannel);
                markRewardClaimed(playerId, streamerChannel, "1hour");
            }
        }

        // Check 2-hour reward
        if (oldWatchTime < REWARD_2_HOURS && newWatchTime >= REWARD_2_HOURS) {
            if (!hasClaimedReward(playerId, streamerChannel, "2hours")) {
                giveKeyReward(player, streamerChannel);
                markRewardClaimed(playerId, streamerChannel, "2hours");
            }
        }
    }
    
    /**
     * Check if player has already claimed a specific reward
     */
    private static boolean hasClaimedReward(UUID playerId, String streamerChannel, String rewardType) {
        try (Jedis jedis = getJedis()) {
            String key = REWARD_CLAIMED_KEY + playerId + ":" + streamerChannel + ":" + rewardType;
            return jedis.exists(key);
        }
    }
    
    /**
     * Mark a reward as claimed
     */
    private static void markRewardClaimed(UUID playerId, String streamerChannel, String rewardType) {
        try (Jedis jedis = getJedis()) {
            String key = REWARD_CLAIMED_KEY + playerId + ":" + streamerChannel + ":" + rewardType;
            jedis.set(key, "claimed");
        }
    }
    
    /**
     * Give enchanted book reward (30 minutes)
     */
    private static void giveEnchantedBookReward(Player player, String streamerChannel) {
        ItemStack enchantedBook = createRandomEnchantedBook();

        if (player.getInventory().firstEmpty() != -1) {
            player.getInventory().addItem(enchantedBook);
            LanguageManager.getInstance().sendMessage(player, "stream_rewards.book_reward", streamerChannel);
        } else {
            player.getWorld().dropItemNaturally(player.getLocation(), enchantedBook);
            LanguageManager.getInstance().sendMessage(player, "stream_rewards.book_dropped", streamerChannel);
        }
    }
    
    /**
     * Give money reward (1 hour)
     */
    private static void giveMoneyReward(Player player, String streamerChannel) {
        Economy.addMoney(player.getUniqueId(), 50000L);
        LanguageManager.getInstance().sendMessage(player, "stream_rewards.money_reward", streamerChannel);
    }
    
    /**
     * Give key reward (2 hours)
     */
    private static void giveKeyReward(Player player, String streamerChannel) {
        ItemStack key = createStreamRewardKey();

        if (player.getInventory().firstEmpty() != -1) {
            player.getInventory().addItem(key);
            LanguageManager.getInstance().sendMessage(player, "stream_rewards.key_reward", streamerChannel);
        } else {
            player.getWorld().dropItemNaturally(player.getLocation(), key);
            LanguageManager.getInstance().sendMessage(player, "stream_rewards.key_dropped", streamerChannel);
        }
    }
    
    /**
     * Create a random enchanted book
     */
    private static ItemStack createRandomEnchantedBook() {
        ItemStack book = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = book.getItemMeta();
        
        // Array of possible enchantments for books
        Enchantment[] enchantments = {
            Enchantment.PROTECTION_ENVIRONMENTAL,
            Enchantment.PROTECTION_FIRE,
            Enchantment.PROTECTION_EXPLOSIONS,
            Enchantment.PROTECTION_PROJECTILE,
            Enchantment.OXYGEN,
            Enchantment.WATER_WORKER,
            Enchantment.THORNS,
            Enchantment.DEPTH_STRIDER,
            Enchantment.DIG_SPEED,
            Enchantment.SILK_TOUCH,
            Enchantment.DURABILITY,
            Enchantment.LOOT_BONUS_BLOCKS,
            Enchantment.DAMAGE_ALL,
            Enchantment.DAMAGE_UNDEAD,
            Enchantment.DAMAGE_ARTHROPODS,
            Enchantment.KNOCKBACK,
            Enchantment.FIRE_ASPECT,
            Enchantment.LOOT_BONUS_MOBS,
            Enchantment.ARROW_DAMAGE,
            Enchantment.ARROW_KNOCKBACK,
            Enchantment.ARROW_FIRE,
            Enchantment.ARROW_INFINITE
        };
        
        Random random = new Random();
        Enchantment randomEnchant = enchantments[random.nextInt(enchantments.length)];
        int randomLevel = random.nextInt(randomEnchant.getMaxLevel()) + 1;
        
        meta.addEnchant(randomEnchant, randomLevel, true);
        meta.setDisplayName("§6§lLivro Encantado de Stream");
        meta.setLore(Arrays.asList("§7Recompensa por assistir stream"));
        
        book.setItemMeta(meta);
        return book;
    }
    
    /**
     * Create a stream reward key
     */
    private static ItemStack createStreamRewardKey() {
        ItemStack key = new ItemStack(Material.TRIPWIRE_HOOK, 1);
        ItemMeta keymeta = key.getItemMeta();
        keymeta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        keymeta.setDisplayName("§a§lCaixa Lápis");
        keymeta.setLore(Arrays.asList("§7Caixa Lápis"));
        key.setItemMeta(keymeta);

        key.addUnsafeEnchantment(Enchantment.DURABILITY, 1);
        return key;
    }
    
    /**
     * Get total watch time for a player and streamer (for debugging/admin commands)
     */
    public static long getWatchTime(UUID playerId, String streamerChannel) {
        try (Jedis jedis = getJedis()) {
            String watchTimeStr = jedis.get(WATCH_TIME_KEY + playerId + ":" + streamerChannel);
            return watchTimeStr != null ? Long.parseLong(watchTimeStr) : 0;
        }
    }
    
    /**
     * Reset all rewards for a player and streamer (for admin use)
     */
    public static void resetPlayerRewards(UUID playerId, String streamerChannel) {
        try (Jedis jedis = getJedis()) {
            // Remove watch time
            jedis.del(WATCH_TIME_KEY + playerId + ":" + streamerChannel);

            // Remove all reward claims
            jedis.del(REWARD_CLAIMED_KEY + playerId + ":" + streamerChannel + ":30min");
            jedis.del(REWARD_CLAIMED_KEY + playerId + ":" + streamerChannel + ":1hour");
            jedis.del(REWARD_CLAIMED_KEY + playerId + ":" + streamerChannel + ":2hours");
        }

        // Remove from memory if present
        if (playerWatchTimes.containsKey(playerId)) {
            playerWatchTimes.get(playerId).remove(streamerChannel);
        }
        if (playerStartTimes.containsKey(playerId)) {
            playerStartTimes.get(playerId).remove(streamerChannel);
        }
    }

    /**
     * Clean up when player leaves
     */
    public static void onPlayerLeave(Player player) {
        stopAllTracking(player);
        UUID playerId = player.getUniqueId();
        playerWatchTimes.remove(playerId);
        playerStartTimes.remove(playerId);
    }
}
