package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class LanguageManager {
    
    private static LanguageManager instance;
    private final Rankup plugin;
    private final Map<String, FileConfiguration> languages = new HashMap<>();
    private final Map<UUID, String> playerLanguages = new HashMap<>();
    private String defaultLanguage = "pt";
    
    public LanguageManager(Rankup plugin) {
        this.plugin = plugin;
        instance = this;
        loadLanguages();
    }
    
    public static LanguageManager getInstance() {
        return instance;
    }
    
    private void loadLanguages() {
        // Create language files if they don't exist
        createLanguageFile("messages_pt.yml");
        createLanguageFile("messages_en.yml");
        
        // Load Portuguese
        File ptFile = new File(plugin.getDataFolder(), "messages_pt.yml");
        if (ptFile.exists()) {
            languages.put("pt", YamlConfiguration.loadConfiguration(ptFile));
        }
        
        // Load English
        File enFile = new File(plugin.getDataFolder(), "messages_en.yml");
        if (enFile.exists()) {
            languages.put("en", YamlConfiguration.loadConfiguration(enFile));
        }
        
        plugin.getLogger().info("Loaded " + languages.size() + " language files");
    }
    
    private void createLanguageFile(String fileName) {
        File file = new File(plugin.getDataFolder(), fileName);
        if (!file.exists()) {
            try {
                plugin.getDataFolder().mkdirs();
                InputStream inputStream = plugin.getResource(fileName);
                if (inputStream != null) {
                    Files.copy(inputStream, file.toPath());
                } else {
                    file.createNewFile();
                }
            } catch (IOException e) {
                plugin.getLogger().severe("Could not create " + fileName);
                e.printStackTrace();
            }
        }
    }
    
    public void setPlayerLanguage(UUID playerId, String language) {
        if (languages.containsKey(language)) {
            playerLanguages.put(playerId, language);
            // Record change to be sent to proxy
            Rankup.getInstance().deltaManager.recordLanguageChange(playerId, language);
        }
    }
    
    public String getPlayerLanguage(UUID playerId) {
        return playerLanguages.getOrDefault(playerId, defaultLanguage);
    }
    
    public void loadPlayerLanguage(Player player) {
        // Get language from PlayerData received from proxy
        PlayerData data = Rankup.getInstance().pendingPlayerData.get(player.getUniqueId());
        if (data != null && data.getLanguage() != null && languages.containsKey(data.getLanguage())) {
            playerLanguages.put(player.getUniqueId(), data.getLanguage());
        } else {
            playerLanguages.put(player.getUniqueId(), defaultLanguage);
        }
    }
    
    public String getMessage(UUID playerId, String key) {
        String language = getPlayerLanguage(playerId);
        return getMessage(language, key);
    }
    
    public String getMessage(Player player, String key) {
        return getMessage(player.getUniqueId(), key);
    }
    
    public String getMessage(String language, String key) {
        FileConfiguration config = languages.get(language);
        if (config == null) {
            config = languages.get(defaultLanguage);
        }
        
        if (config != null && config.contains(key)) {
            return ChatColor.translateAlternateColorCodes('&', config.getString(key));
        }
        
        // Fallback to default language
        if (!language.equals(defaultLanguage)) {
            FileConfiguration defaultConfig = languages.get(defaultLanguage);
            if (defaultConfig != null && defaultConfig.contains(key)) {
                return ChatColor.translateAlternateColorCodes('&', defaultConfig.getString(key));
            }
        }
        
        return "Missing translation: " + key;
    }
    
    public String getMessage(UUID playerId, String key, String... replacements) {
        String message = getMessage(playerId, key);
        return formatMessage(message, replacements);
    }
    
    public String getMessage(Player player, String key, String... replacements) {
        return getMessage(player.getUniqueId(), key, replacements);
    }
    
    public String getMessage(String language, String key, String... replacements) {
        String message = getMessage(language, key);
        return formatMessage(message, replacements);
    }
    
    private String formatMessage(String message, String... replacements) {
        for (int i = 0; i < replacements.length; i++) {
            message = message.replace("{" + i + "}", replacements[i]);
        }
        return message;
    }
    
    public void sendMessage(Player player, String key) {
        player.sendMessage(getMessage(player, key));
    }
    
    public void sendMessage(Player player, String key, String... replacements) {
        player.sendMessage(getMessage(player, key, replacements));
    }
    
    public void reloadLanguages() {
        languages.clear();
        loadLanguages();
    }
    
    public boolean isValidLanguage(String language) {
        return languages.containsKey(language);
    }
    
    public String[] getAvailableLanguages() {
        return languages.keySet().toArray(new String[0]);
    }
}
