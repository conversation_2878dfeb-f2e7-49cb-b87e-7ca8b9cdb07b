package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitScheduler;
import org.json.JSONObject;

import java.io.*;
import java.net.Socket;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

public class TwitchManager {

    // In-memory cache for player Twitch channels
    private static final Map<UUID, String> playerTwitch = new HashMap<>();

    // Verification system (temporary data, not persisted)
    private static final Map<String, String> playerVerificationCode = new HashMap<>();
    private static final Map<UUID, String> playerVerificationChannel = new HashMap<>();

    // List of all registered Twitch channels (loaded from PlayerData)
    private static final ArrayList<String> twitchList = new ArrayList<>();

    // List of currently live channels
    private static final ArrayList<String> channelLive = new ArrayList<>();

    /**
     * Set a player's Twitch channel (called from PlayerData system)
     */
    public static void setTwitchChannel(UUID playerId, String twitchChannel) {
        if (twitchChannel != null && !twitchChannel.isEmpty()) {
            playerTwitch.put(playerId, twitchChannel);

            // Add to the global list if not already present
            if (!twitchList.contains(twitchChannel)) {
                twitchList.add(twitchChannel);
            }
        }
    }

    /**
     * Remove a player's Twitch channel
     */
    public static void removeTwitchChannel(UUID playerId) {
        String oldChannel = playerTwitch.remove(playerId);
        if (oldChannel != null) {
            // Remove from live list if present
            channelLive.remove(oldChannel);

            // Check if any other player still uses this channel
            boolean stillInUse = playerTwitch.containsValue(oldChannel);
            if (!stillInUse) {
                twitchList.remove(oldChannel);
            }

            // Record the change in delta system
            Rankup.getInstance().deltaManager.recordTwitchChannelChange(playerId, "");
        }
    }

    public static Boolean checkTwitchChannel(String twitchChannel) {
        return twitchList.contains(twitchChannel);
    }

    public static Boolean checkPlayerTwitch(UUID playerId) {
        return playerTwitch.containsKey(playerId);
    }

    /**
     * Assign a Twitch channel to a player (used by admin commands and verification)
     */
    public static void assignTwitchChannelToPlayer(UUID playerId, String twitchChannel) {
        // Remove old channel if exists
        String oldChannel = playerTwitch.get(playerId);
        if (oldChannel != null) {
            removeTwitchChannel(playerId);
        }

        // Set new channel
        playerTwitch.put(playerId, twitchChannel);
        if (!twitchList.contains(twitchChannel)) {
            twitchList.add(twitchChannel);
        }

        // Record the change in delta system
        Rankup.getInstance().deltaManager.recordTwitchChannelChange(playerId, twitchChannel);
    }

    public static String getTwitchChannel(UUID playerId) {
        return playerTwitch.get(playerId);
    }



    public static String generateVerificationCode(Player p, String twitchChannel) {
        StringBuilder code = new StringBuilder(9);  // Predefine capacity for better memory management

        // Loop through 8 characters (4 + 4), with a dash in between
        for (int i = 0; i < 9; i++) {
            if (i == 4) {
                code.append('-');  // Insert the dash after the first 4 characters
            } else {
                char randomChar = (char) (ThreadLocalRandom.current().nextInt(26) + 'A');
                code.append(randomChar);
            }
        }

        playerVerificationCode.put(twitchChannel, code.toString());
        playerVerificationChannel.put(p.getUniqueId(), twitchChannel);

        return code.toString();
    }

    public static void verifyVerificationCode(Player p) {
        p.sendMessage("§aA verificar o código na twitch...");
        if (playerVerificationChannel.containsKey(p.getUniqueId())) {
            try {
                Bukkit.getScheduler().scheduleSyncDelayedTask(Rankup.getInstance(), () -> {
                    String code = playerVerificationCode.get(playerVerificationChannel.get(p.getUniqueId()));
                    String description = "";
                    String hostname = "127.0.0.1";
                    int port = 65432;

                    try (Socket socket = new Socket(hostname, port)) {

                        description = getResponse(socket, p);

                    } catch (UnknownHostException ex) {
                        System.out.println("Server not found: " + ex.getMessage());
                        p.sendMessage("§cErro ao verificar o código. Tenta novamente.");
                    } catch (IOException ex) {
                        System.out.println("I/O error: " + ex.getMessage());
                        p.sendMessage("§cErro ao verificar o código. Tenta novamente.");
                    }
                    if (description.contains(code)) {
                        p.sendMessage("§aCódigo verificado com sucesso! Canal associado.");
                        String channelToAssign = playerVerificationChannel.get(p.getUniqueId());

                        // Use the new assignment method which handles delta recording
                        assignTwitchChannelToPlayer(p.getUniqueId(), channelToAssign);

                        // Clean up verification data
                        playerVerificationCode.remove(channelToAssign);
                        playerVerificationChannel.remove(p.getUniqueId());
                    } else {
                        p.sendMessage("§cCódigo inválido. Tenta novamente.");
                    }
                }, 0L);
            }catch (Exception e) {
                p.sendMessage("§cOcorreu um erro ao verificar o código na Twitch.");
            }
        }else {
            p.sendMessage("§cNão tens nenhum código de verificação pendente.");
        }
    }

    private static String getResponse(Socket socket, Player p) throws IOException {
        OutputStream output = socket.getOutputStream();
        PrintWriter writer = new PrintWriter(output, true);

        BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));

        // Create a JSON object with type and channel
        JSONObject json = new JSONObject();
        json.put("type", "checkDescription");
        json.put("channel", playerVerificationChannel.get(p.getUniqueId()));

        // Send the JSON object as a string
        writer.println(json.toString());

        return reader.readLine();
    }

    public static ArrayList<String> getChannelLive() {
        return channelLive;
    }

    public static void setChannelLive(String channel) {
        channelLive.add(channel);
        Bukkit.getScheduler().runTaskAsynchronously(Rankup.getInstance(), () -> {
            try {
                JSONObject jsonObject = new JSONObject();
                Socket socket = new Socket("localhost", 12345);
                DataOutputStream out = new DataOutputStream(socket.getOutputStream());
                DataInputStream din=new DataInputStream(socket.getInputStream());
                jsonObject.put("type", "joinChannel");
                jsonObject.put("channel", channel);
                out.writeUTF(jsonObject.toString());
                out.flush();
                socket.close();
                din.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static void removeChannelLive(String channel) {
        channelLive.remove(channel);
        Bukkit.getScheduler().runTaskAsynchronously(Rankup.getInstance(), () -> {
            try {
                JSONObject jsonObject = new JSONObject();
                Socket socket = new Socket("localhost", 12345);
                DataOutputStream out = new DataOutputStream(socket.getOutputStream());
                DataInputStream din=new DataInputStream(socket.getInputStream());
                jsonObject.put("type", "leaveChannel");
                jsonObject.put("channel", channel);
                out.writeUTF(jsonObject.toString());
                out.flush();
                socket.close();
                din.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

}
