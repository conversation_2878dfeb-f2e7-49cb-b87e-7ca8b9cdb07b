package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Tuple;

import java.util.*;

public class KillDeathManager {
    // In-memory cache
    private static final Map<UUID, Long> playerKills = new HashMap<>();
    private static final Map<UUID, Long> playerDeaths = new HashMap<>();

    // Redis key prefixes
    private static final String PLAYER_KILLS_KEY = "player:kills:";
    private static final String PLAYER_DEATHS_KEY = "player:deaths:";

    // Utility method to get Jedis connection
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    // Load clan data from Redis to cache
    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            playerKills.put(playerId, parseLong(jedis.zscore(PLAYER_KILLS_KEY, playerId.toString())));
            playerDeaths.put(playerId, parseLong(jedis.zscore(PLAYER_DEATHS_KEY, playerId.toString())));
        }
    }

    // Helper method to parse long safely from Redis data
    private static Long parseLong(Double value) {
        return value != null ? value.longValue() : 0L;
    }

    // Save player clan data from cache to Redis
    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.zadd(PLAYER_KILLS_KEY, playerKills.get(playerId), playerId.toString());
            jedis.zadd(PLAYER_DEATHS_KEY, playerDeaths.get(playerId), playerId.toString());
        }
    }

    public static void addKills(Player player, long kills) {
        UUID playerId = player.getUniqueId();
        Long currentKills = playerKills.get(playerId);
        if (currentKills == null) {
            currentKills = 0L;
        }
        playerKills.put(playerId, currentKills + kills);
        Rankup.getInstance().deltaManager.recordKillsChange(player.getUniqueId(), currentKills + kills);
    }

    public static long getKills(Player player) {
        UUID playerId = player.getUniqueId();
        Long kills = playerKills.get(playerId);
        if (kills == null) {
            return 0;
        }
        return kills;
    }

    public static void setKills(Player player, long kills) {
        UUID playerId = player.getUniqueId();
        playerKills.put(playerId, kills);
        Rankup.getInstance().deltaManager.recordKillsChange(player.getUniqueId(), kills);
    }

    public static void addDeaths(Player player, long deaths) {
        UUID playerId = player.getUniqueId();
        Long currentDeaths = playerDeaths.get(playerId);
        if (currentDeaths == null) {
            currentDeaths = 0L;
        }
        playerDeaths.put(playerId, currentDeaths + deaths);
        Rankup.getInstance().deltaManager.recordDeathsChange(player.getUniqueId(), currentDeaths + deaths);
    }

    public static long getDeaths(Player player) {
        UUID playerId = player.getUniqueId();
        Long deaths = playerDeaths.get(playerId);
        if (deaths == null) {
            return 0;
        }
        return deaths;
    }

    public static void setDeaths(Player player, long deaths) {
        UUID playerId = player.getUniqueId();
        playerDeaths.put(playerId, deaths);
        Rankup.getInstance().deltaManager.recordDeathsChange(player.getUniqueId(), deaths);
    }

    // Get Top 5 players money on redis
    public static Set<Tuple> getTop5Kills() {
        try (Jedis jedis = getJedis()) {
            return jedis.zrevrangeWithScores(PLAYER_KILLS_KEY, 0, 4);
        }
    }

    public static Set<String> getTop5Deaths() {
        try (Jedis jedis = getJedis()) {
            return jedis.zrevrange(PLAYER_DEATHS_KEY, 0, 4);
        }
    }
}
