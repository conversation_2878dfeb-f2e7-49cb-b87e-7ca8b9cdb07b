package me.miguel19877.dev.managers;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Tuple;

import java.util.*;

public class ClanManager {
    // In-memory cache
    private static final Map<UUID, String> playerClans = new HashMap<>();
    private static final Map<String, Clan> clans = new HashMap<>();
    private static final Map<UUID, Set<String>> clanInvitations = new HashMap<>();
    private static final Map<Clan, Long> clanKills = new HashMap<>();
    private static final Map<Clan, Long> clanDeaths = new HashMap<>();

    // Redis key prefixes
    private static final String CLAN_KEY = "clan:";
    private static final String PLAYER_CLAN_KEY = "player:clan:";
    private static final String CLAN_INVITE_KEY = "clan:invite:";
    private static final String CLAN_KILLS_KEY = "clan:kills:";
    private static final String CLAN_DEATHS_KEY = "clan:deaths:";

    // Utility method to get Jedis connection
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    // Load clan data from Redis to cache
    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String clanName = jedis.get(PLAYER_CLAN_KEY + playerId);
            if (clanName != null) {
                playerClans.put(playerId, clanName);
                // Load clan data if not already loaded
                if (!clans.containsKey(clanName)) {
                    String clanData = jedis.get(CLAN_KEY + clanName);
                    if (clanData != null) {
                        clans.put(clanName, Clan.fromString(clanData));
                    }
                }
            }
            Set<String> invites = jedis.smembers(CLAN_INVITE_KEY + playerId);
            if (invites != null) {
                clanInvitations.put(playerId, new HashSet<>(invites));
            }
        }
    }

    // Save player clan data from cache to Redis
    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            String clanName = playerClans.get(playerId);
            if (clanName != null) {
                jedis.set(PLAYER_CLAN_KEY + playerId, clanName);
                jedis.zadd(CLAN_KILLS_KEY, clanKills.getOrDefault(clans.get(clanName), 0L), clanName);
                jedis.zadd(CLAN_DEATHS_KEY, clanDeaths.getOrDefault(clans.get(clanName), 0L), clanName);
            }
            Set<String> invites = clanInvitations.get(playerId);
            if (!invites.isEmpty()) {
                jedis.sadd(CLAN_INVITE_KEY + playerId, invites.toArray(new String[0]));
            }
        }
    }

    // Create a new clan
    public static boolean createClan(String clanName, Player owner) {
        try (Jedis jedis = getJedis()) {
            if (jedis.exists(CLAN_KEY + clanName)) {
                return false; // Clan name already exists in Redis
            }
        }

        UUID ownerId = owner.getUniqueId();
        Clan clan = new Clan(clanName, ownerId);
        clans.put(clanName, clan);
        playerClans.put(ownerId, clanName);
        try (Jedis jedis = getJedis()) {
            jedis.set(CLAN_KEY + clanName, clan.toString());
            jedis.set(PLAYER_CLAN_KEY + ownerId, clanName);
        }
        return true;
    }

    public static void addPlayerToClan(String clanName, Player player) {
        UUID playerId = player.getUniqueId();
        Clan clan = clans.get(clanName);
        if (clan != null) {
            clan.addMember(playerId);
            playerClans.put(playerId, clanName);
            clanInvitations.remove(playerId);  // Clear invites once player joins
            try (Jedis jedis = getJedis()) {
                jedis.set(CLAN_KEY + clanName, clan.toString());
                jedis.set(PLAYER_CLAN_KEY + playerId, clanName);
                jedis.del(CLAN_INVITE_KEY + playerId); // Remove all invites for player
            }
        }
    }

    public static void removePlayerFromClan(Player player) {
        UUID playerId = player.getUniqueId();
        String clanName = playerClans.get(playerId);
        if (clanName != null) {
            Clan clan = clans.get(clanName);
            if (clan != null) {
                clan.removeMember(playerId);
                playerClans.remove(playerId);
                try (Jedis jedis = getJedis()) {
                    jedis.set(CLAN_KEY + clanName, clan.toString());
                    jedis.del(PLAYER_CLAN_KEY + playerId);
                }
            }
        }
    }

    // Delete a clan
    public static void deleteClan(String clanName) {
        Clan clan = clans.get(clanName);
        if (clan != null) {
            for (UUID playerId : clan.getMembers()) {
                playerClans.remove(playerId);
                try (Jedis jedis = getJedis()) {
                    jedis.del(PLAYER_CLAN_KEY + playerId);
                }
            }
            clans.remove(clanName);
            try (Jedis jedis = getJedis()) {
                jedis.del(CLAN_KEY + clanName);
            }
        }
    }

    // Boolean method to check if a player is in a clan
    public static boolean isPlayerInClan(UUID playerId) {
        return playerClans.containsKey(playerId);
    }

    // Boolean method to check if a player is the leader of a clan
    public static boolean isPlayerClanLeader(UUID playerId) {
        String clanName = playerClans.get(playerId);
        if (clanName != null) {
            Clan clan = clans.get(clanName);
            return clan != null && clan.getOwner().equals(playerId);
        }
        return false;
    }

    // Helper methods to get player clan information
    public static String getClanName(UUID playerId) {
        return playerClans.get(playerId);
    }

    public static Clan getClan(String clanName) {
        return clans.get(clanName);
    }

    // Invitation related methods
    public static void invitePlayerToClan(String clanName, Player player) {
        UUID playerId = player.getUniqueId();
        Set<String> invites = clanInvitations.getOrDefault(playerId, new HashSet<>());
        invites.add(clanName);
        clanInvitations.put(playerId, invites);
        try (Jedis jedis = getJedis()) {
            jedis.sadd(CLAN_INVITE_KEY + playerId, clanName);
        }
    }

    public static boolean hasInvite(UUID playerId, String clanName) {
        Set<String> invites = clanInvitations.get(playerId);
        return invites != null && invites.contains(clanName);
    }

    public static void revokeInvite(UUID playerId, String clanName) {
        Set<String> invites = clanInvitations.get(playerId);
        if (invites != null) {
            invites.remove(clanName);
            try (Jedis jedis = getJedis()) {
                jedis.srem(CLAN_INVITE_KEY + playerId, clanName);
            }
        }
    }

    public static void tryToAdd1KillForClan(Player player) {
        UUID playerId = player.getUniqueId();
        String clanName = playerClans.get(playerId);
        if (clanName != null) {
            Clan clan = clans.get(clanName);
            if (clan != null) {
                addKill(clan);
                try (Jedis jedis = getJedis()) {
                    jedis.set(CLAN_KEY + clanName, clan.toString());
                }
            }
        }
    }

    public static void tryToAdd1DeathForClan(Player player) {
        UUID playerId = player.getUniqueId();
        String clanName = playerClans.get(playerId);
        if (clanName != null) {
            Clan clan = clans.get(clanName);
            if (clan != null) {
                addDeath(clan);
                try (Jedis jedis = getJedis()) {
                    jedis.set(CLAN_KEY + clanName, clan.toString());
                }
            }
        }
    }

    public static void addKill(Clan clan) {
        clanKills.put(clan, clanKills.getOrDefault(clan, 0L) + 1);
    }

    public static void addDeath(Clan clan) {
        clanDeaths.put(clan, clanDeaths.getOrDefault(clan, 0L) + 1);
    }

    public static Set<Tuple> getTop5Kills() {
        try (Jedis jedis = getJedis()) {
            return jedis.zrevrangeWithScores(CLAN_KILLS_KEY, 0, 4);
        }
    }

    public static Set<Tuple> getTop5Deaths() {
        try (Jedis jedis = getJedis()) {
            return jedis.zrevrangeWithScores(CLAN_DEATHS_KEY, 0, 4);
        }
    }
}
