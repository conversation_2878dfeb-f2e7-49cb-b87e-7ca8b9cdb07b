package me.miguel19877.dev.managers;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

public class RedisManager {
    private static JedisPool pool;

    public static void connect(String host, int port) {
        pool = new JedisPool(host, port);
    }

    public static Jedis getJedis() {
        return pool.getResource();
    }

    // Call this method during server shutdown
    public static void closePool() {
        pool.close();
    }
}
