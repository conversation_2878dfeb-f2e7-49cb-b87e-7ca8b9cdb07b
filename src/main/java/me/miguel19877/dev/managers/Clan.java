package me.miguel19877.dev.managers;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class Clan {
    private String name;
    private UUID owner;
    private Set<UUID> members;

    public Clan(String name, UUID owner) {
        this.name = name;
        this.owner = owner;
        this.members = new HashSet<>();
        this.members.add(owner);
    }

    public String getName() {
        return name;
    }

    public UUID getOwner() {
        return owner;
    }

    public Set<UUID> getMembers() {
        return members;
    }

    public void addMember(UUID playerId) {
        members.add(playerId);
    }

    public void removeMember(UUID playerId) {
        members.remove(playerId);
    }

    @Override
    public String toString() {
        return name + ":" + owner + ":" + members.toString();
    }

    public static Clan fromString(String data) {
        String[] parts = data.split(":");
        Clan clan = new Clan(parts[0], UUID.fromString(parts[1]));
        String membersStr = parts[2].substring(1, parts[2].length() - 1); // Remove brackets
        for (String memberId : membersStr.split(", ")) {
            clan.addMember(UUID.fromString(memberId));
        }
        return clan;
    }
}
