package me.miguel19877.dev.managers;

import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages shop and selling related achievements
 */
public class AchievementShopManager {
    
    private static AchievementShopManager instance;
    
    // Track selling statistics
    private final Map<UUID, Integer> totalSales = new HashMap<>();
    private final Map<UUID, Long> totalSaleValue = new HashMap<>();
    private final Map<UUID, Integer> totalBlocksSold = new HashMap<>();
    private final Map<UUID, Boolean> hasEverSold = new HashMap<>();
    
    private AchievementShopManager() {}
    
    public static AchievementShopManager getInstance() {
        if (instance == null) {
            instance = new AchievementShopManager();
        }
        return instance;
    }
    
    /**
     * Called when a player sells items/blocks
     */
    public void onPlayerSell(Player player, int itemCount, long saleValue) {
        UUID playerId = player.getUniqueId();
        
        // Track first sale - dia_de_pagamento
        if (!hasEverSold.getOrDefault(playerId, false)) {
            hasEverSold.put(playerId, true);
            AchievementHelper.setProgress(player, "dia_de_pagamento", 1);
        }
        
        // Update statistics
        int currentSales = totalSales.getOrDefault(playerId, 0) + 1;
        long currentValue = totalSaleValue.getOrDefault(playerId, 0L) + saleValue;
        int currentBlocks = totalBlocksSold.getOrDefault(playerId, 0) + itemCount;
        
        totalSales.put(playerId, currentSales);
        totalSaleValue.put(playerId, currentValue);
        totalBlocksSold.put(playerId, currentBlocks);
        
        // Check achievements
        checkSaleAchievements(player, currentSales, currentValue, currentBlocks);
    }
    
    /**
     * Check sale-related achievements
     */
    private void checkSaleAchievements(Player player, int totalSalesCount, long totalValue, int totalBlocksSold) {
        // negociante_de_rua - Sell more than 10,000 blocks
        if (totalBlocksSold >= 10000) {
            AchievementHelper.setProgress(player, "negociante_de_rua", totalBlocksSold);
        }
        
        // comerciante_solido - Make 250 sales in /shop
        if (totalSalesCount >= 250) {
            AchievementHelper.setProgress(player, "comerciante_solido", totalSalesCount);
        }
        
        // negocio_fechado - Sell more than 1,000,000 worth in blocks at /loja
        if (totalValue >= 1000000) {
            AchievementHelper.setProgress(player, "negocio_fechado", (int) Math.min(totalValue, Integer.MAX_VALUE));
        }
        
        // comerciante_lendario - Sell a total of 10,000,000 in blocks and items
        if (totalValue >= 10000000) {
            AchievementHelper.setProgress(player, "comerciante_lendario", (int) Math.min(totalValue, Integer.MAX_VALUE));
        }
    }
    
    /**
     * Get total sales for a player
     */
    public int getTotalSales(UUID playerId) {
        return totalSales.getOrDefault(playerId, 0);
    }
    
    /**
     * Get total sale value for a player
     */
    public long getTotalSaleValue(UUID playerId) {
        return totalSaleValue.getOrDefault(playerId, 0L);
    }
    
    /**
     * Get total blocks sold for a player
     */
    public int getTotalBlocksSold(UUID playerId) {
        return totalBlocksSold.getOrDefault(playerId, 0);
    }
    
    /**
     * Check if player has ever sold anything
     */
    public boolean hasEverSold(UUID playerId) {
        return hasEverSold.getOrDefault(playerId, false);
    }
    
    /**
     * Clean up player data when they leave
     */
    public void cleanupPlayer(UUID playerId) {
        // Keep the data for when they return
        // In a full implementation, you might save to database here
    }
    
    /**
     * Load player data (if you have persistent storage)
     */
    public void loadPlayerData(Player player) {
        // In a full implementation, you would load from database/Redis here
        UUID playerId = player.getUniqueId();
        if (!totalSales.containsKey(playerId)) {
            totalSales.put(playerId, 0);
            totalSaleValue.put(playerId, 0L);
            totalBlocksSold.put(playerId, 0);
            hasEverSold.put(playerId, false);
        }
    }
}
