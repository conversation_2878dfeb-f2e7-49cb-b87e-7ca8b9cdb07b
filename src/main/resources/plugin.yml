name: CAH
version: '${project.version}'
main: me.miguel19877.dev.Rankup
authors: [ miguel19877 ]
description: CAH
commands:
  grupo:
    description: Comando para grupos
  kit:
    description: Comando para kits
  rankup:
    description: Comando para rankup
  darcaixa:
    description: Comando para dar caixas
  spawn:
    description: Comando para spawn
  money:
    description: Comando para money
  chat:
    description: Comando para chat
  minas:
    description: Comando para minas
  loja:
    description: Comando para loja
  encantamentos:
    description: Comando para encantamentos
  skywars:
    description: Comando para skywars
  evento:
    description: Comando para evento
  warp:
    description: Comando para warp
  warps:
    description: Comando para warps
  lobby:
    description: Comando para lobby
  setarkit:
    description: Comando para setar kit
  clan:
    description: Comando para clan
  votar:
    description: Comando para votar
  rank:
    description: Comando para rank
  mina:
    description: Comando para mina
  minapvp:
    description: Comando para minapvp
  reparar:
    description: Comando para reparar
  mudarpassword:
    description: Comando para mudar password
  twitch:
    description: Comando para twitch
  live:
    description: Comando para live
  privatemsg:
    description: Comando para privatemsg
  msg:
    description: Comando para msg
  pm:
    description: Comando para pm
  tell:
    description: Comando para tell
  whisper:
    description: Comando para whisper
  bedwars:
    description: BedWars minigame command
    usage: /bedwars <join|leave|list|shop|stats|create>
    aliases: [bw]
  1v1:
    description: Command to manage 1v1 PvP Events
    usage: /1v1 <start|join|setloc1|setloc2|lobby>
  prestige:
    description: Prestige to gain benefits.
    usage: /prestige
  streamreward:
    description: Comando para gerenciar recompensas de stream
    usage: /streamreward <check|reset|info>
  achievements:
    description: View your achievements
    usage: /achievements
    aliases: [ach, conquistas]
  combatlog:
    description: Combat log management command
    usage: /combatlog <test|check|list|clear> [player]
  tpa:
    description: Send a teleport request to another player
    usage: /tpa <player>
  tpaccept:
    description: Accept a teleport request
    usage: /tpaccept
  tpdeny:
    description: Deny a teleport request
    usage: /tpdeny
  broadcast:
    description: Broadcast a message to all players
    usage: /broadcast <message>
    aliases: [bc, bcast, anuncio, anunciar]