# Achievement GUI Customization Guide

## 🎨 **GUI Overview**

The achievement system now features a beautiful GUI with two main screens:

### **Main GUI (Category Selection)**
- **Size**: 27 slots (3 rows)
- **Layout**: 5 player heads representing categories in the middle row
- **Categories**: Bronze, Silver, Gold, Platinum, Mythic
- **Features**: Shows completion statistics for each category

### **Category GUI (Individual Achievements)**
- **Size**: 54 slots (6 rows)
- **Layout**: Achievement items displayed in a grid pattern
- **Features**: Progress bars, completion status, rewards info, back button

## 🏆 **Customizing Category Skulls**

To customize the skull textures for each category, modify the `createCategoryHead` method in `AchievementGUI.java`:

### **Method 1: Player Skulls**
```java
// In the switch statement around line 100:
case "bronze":
    meta.setOwner("PlayerNameHere");
    break;
case "silver":
    meta.setOwner("AnotherPlayerName");
    break;
```

### **Method 2: Custom Textures (Recommended)**
```java
// You'll need to import these:
import com.destroystokyo.paper.profile.PlayerProfile;
import java.net.URL;
import java.util.UUID;

// In the switch statement:
case "bronze":
    try {
        PlayerProfile profile = Bukkit.createPlayerProfile(UUID.randomUUID());
        profile.getTextures().setSkin(new URL("https://textures.minecraft.net/texture/YOUR_TEXTURE_ID"));
        meta.setOwnerProfile(profile);
    } catch (Exception e) {
        // Fallback to default
    }
    break;
```

### **Method 3: Base64 Textures**
```java
// For offline/custom textures, you can use base64 encoded textures
// This requires additional reflection or libraries like SkullCreator
```

## 🎨 **Suggested Category Themes**

### **Bronze Category**
- **Theme**: Copper/Bronze materials
- **Suggested Texture**: Bronze-colored helmet or pickaxe
- **Color Scheme**: §6 (Gold/Orange)

### **Silver Category**
- **Theme**: Iron/Silver materials
- **Suggested Texture**: Iron helmet or sword
- **Color Scheme**: §7 (Gray)

### **Gold Category**
- **Theme**: Gold materials
- **Suggested Texture**: Golden crown or ingot
- **Color Scheme**: §e (Yellow)

### **Platinum Category**
- **Theme**: Diamond/Platinum materials
- **Suggested Texture**: Diamond or crystal
- **Color Scheme**: §b (Aqua)

### **Mythic Category**
- **Theme**: Legendary/Magical items
- **Suggested Texture**: Dragon head, nether star, or enchanted item
- **Color Scheme**: §5 (Purple)

## 🔧 **GUI Layout Customization**

### **Main GUI Slots**
```
[ ][ ][ ][ ][ ][ ][ ][ ][ ]  Row 1 (0-8)
[ ][B][S][G][P][M][ ][ ][ ]  Row 2 (9-17) - Categories
[ ][ ][ ][ ][ ][ ][ ][ ][ ]  Row 3 (18-26)

B = Bronze (slot 10)
S = Silver (slot 11)  
G = Gold (slot 12)
P = Platinum (slot 13)
M = Mythic (slot 14)
```

### **Category GUI Slots**
```
[ ][ ][ ][ ][ ][ ][ ][ ][ ]  Row 1 (0-8) - Glass panes
[ ][A][A][A][A][A][A][A][ ]  Row 2 (9-17) - Achievements
[ ][A][A][A][A][A][A][A][ ]  Row 3 (18-26) - Achievements  
[ ][A][A][A][A][A][A][A][ ]  Row 4 (27-35) - Achievements
[ ][A][A][A][A][A][A][A][ ]  Row 5 (36-44) - Achievements
[ ][ ][ ][ ][B][ ][ ][ ][ ]  Row 6 (45-53) - Back button

A = Achievement items
B = Back button (slot 49)
```

## 🎯 **Achievement Item Features**

### **Completed Achievements**
- ✅ Green checkmark prefix
- 🌟 Enchantment glow effect
- 📅 Completion date display
- 💰 Rewards already earned

### **In-Progress Achievements**
- ○ Gray circle prefix
- 📊 Progress bar (20 characters)
- 📈 Percentage completion
- 🎁 Available rewards preview

### **Progress Bar Example**
```
§7[§a■■■■■■■■§7■■■■■■■■■■■■] §7(40%)
```

## 🎨 **Color Schemes**

### **Achievement Status Colors**
- **Completed**: §a (Green)
- **In Progress**: §7 (Gray)
- **Progress Numbers**: §e (Yellow)
- **Descriptions**: §7 (Gray)
- **Rewards**: §6 (Gold)

### **Category Colors**
- **Bronze**: §6 (Gold/Orange)
- **Silver**: §7 (Gray)
- **Gold**: §e (Yellow)
- **Platinum**: §b (Aqua)
- **Mythic**: §5 (Purple)

## 🔧 **Advanced Customization**

### **Adding Sound Effects**
```java
// In AchievementGUIListener.java, add sounds:
player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
```

### **Adding Particles**
```java
// When opening GUIs:
player.spawnParticle(Particle.VILLAGER_HAPPY, player.getLocation(), 10);
```

### **Custom Lore Formatting**
Modify the `createAchievementItem` method in `AchievementCategoryGUI.java` to customize:
- Progress bar styles
- Reward descriptions
- Date formatting
- Additional information

### **GUI Animations**
You can add opening/closing animations by scheduling tasks that modify items over time.

## 📱 **Mobile-Friendly Features**

The GUI is designed to work well on both desktop and mobile Minecraft:
- Large, clear icons
- Readable text sizes
- Intuitive navigation
- Touch-friendly button placement

## 🚀 **Ready to Use**

The GUI system is fully functional and includes:
- ✅ Category-based organization
- ✅ Progress tracking visualization
- ✅ Completion status indicators
- ✅ Reward information display
- ✅ Intuitive navigation
- ✅ Multi-language support ready
- ✅ Mobile-friendly design

Simply customize the skull textures and you're ready to go!
