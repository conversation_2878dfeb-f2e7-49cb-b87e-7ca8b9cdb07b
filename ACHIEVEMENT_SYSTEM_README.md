# Achievement System Implementation

## Overview
This document describes the achievement system implementation for the Spigot server that integrates with the Velocity proxy.

## Architecture

### Core Components

1. **Achievement Data Classes**
   - `Achievement.java` - Represents achievement definitions
   - `AchievementPlayerData.java` - Stores player progress and completions

2. **Manager Classes**
   - `AchievementManager.java` - Main system manager (Singleton)
   - Handles caching, progress tracking, and Velocity communication

3. **Listeners**
   - `AchievementMessageListener.java` - Handles plugin messages from Velocity
   - Processes INIT messages with achievement definitions and player data

4. **Commands**
   - `Achievements.java` - `/achievements` command to view progress
   - Aliases: `/ach`, `/conquistas`

5. **Utilities**
   - `AchievementHelper.java` - Convenience methods for progress tracking

## Plugin Messaging Protocol

### Channel: `"ach:main"`

### Message Types:

#### INIT Message (Velocity → Spigot)
```json
{
  "subChannel": "INIT",
  "playerId": "uuid-string",
  "achievements": {
    "achievement_id": {
      "id": "achievement_id",
      "name": "Achievement Name",
      "description": ["Description line 1", "Description line 2"],
      "icon": "MATERIAL_NAME",
      "threshold": 100,
      "category": "bronze",
      "commands": ["money add %player% 500", "give %player% diamond 1"]
    }
  },
  "playerData": {
    "playerId": "uuid-string",
    "progress": {
      "achievement_id": 25
    },
    "completions": {
      "completed_achievement_id": 1640995200000
    }
  }
}
```

#### UPDATE Message (Spigot → Velocity)
```json
{
  "playerId": "uuid-string",
  "achievementId": "achievement_id",
  "progress": 26
}
```

## Integration Points

### Main Plugin Class (Rankup.java)
- Registers `ach:main` plugin messaging channel
- Registers `AchievementMessageListener`
- Registers `/achievements` command

### Player Lifecycle
- **Join**: Achievement data loaded via INIT message
- **Quit**: Achievement cache cleaned up

### Language Support
Added language keys to both `messages_pt.yml` and `messages_en.yml`:
- `achievement.completed` - Achievement completion message
- `achievement.not_loaded` - Data not loaded message
- `achievement.none_available` - No achievements message
- `achievement.list_header` - Achievement list header
- `achievement.list_footer` - Achievement list footer

## Usage Examples

### For Achievement Tracking (Future Implementation)
```java
// In event listeners, increment progress
AchievementHelper.incrementProgress(player, "blocks_mined");
AchievementHelper.incrementProgress(player, "monster_kills", 1);

// Set specific progress
AchievementHelper.setProgress(player, "playtime_hours", 5);

// Check completion
if (AchievementHelper.hasCompleted(player, "first_kill")) {
    // Do something special
}
```

### Player Commands
- `/achievements` - View all achievements with progress
- `/ach` - Short alias
- `/conquistas` - Portuguese alias

## Data Flow

1. **Player joins server** → Velocity sends INIT message with achievements + progress
2. **AchievementMessageListener** processes INIT → Stores data in AchievementManager
3. **Player performs action** → Event listener calls AchievementHelper.incrementProgress()
4. **AchievementManager** checks for completion → Executes rewards if completed
5. **Progress update** sent to Velocity via plugin messaging
6. **Player leaves** → Achievement cache cleaned up

## Achievement Completion Handling

When an achievement is completed:
1. Completion message shown to player (localized)
2. Reward commands executed on main thread
3. Completion time recorded
4. Progress update sent to Velocity

## Categories

Achievements are organized by categories:
- `bronze` - Bronze achievements
- `silver` - Silver achievements  
- `gold` - Gold achievements
- `diamond` - Diamond achievements
- `special` - Special achievements
- `general` - General achievements (default)

## Thread Safety

- Uses `ConcurrentHashMap` for thread-safe caching
- Reward commands executed on main thread via scheduler
- Plugin messaging handled asynchronously

## Error Handling

- Graceful handling of missing achievement data
- Fallback messages when translations missing
- Logging for debugging achievement system issues
- Safe handling of malformed JSON messages

## Achievement Tracking Implementation

### ✅ **Implemented Achievement Types**

#### **Bronze Achievements**
- `primeira_picareta` - Craft first pickaxe ✅
- `batismo_de_pedra` - Mine 500 blocks ✅
- `minerador_junior` - Reach rank Aprendiz ✅
- `maos_sujas` - Collect 100 coal blocks ✅
- `dia_de_pagamento` - Sell first blocks ✅
- `explorador_local` - Visit 3 different mines ✅
- `ferramenta_partida` - Break a stone pickaxe ✅
- `pe_de_meia` - Reach 1,000,000 money ✅
- `inicio_de_jornada` - Play 1 hour total ✅
- `recruta_de_clan` - Join a clan ✅

#### **Silver Achievements**
- `minerador_consistente` - Mine 25,000 blocks ✅
- `subterraneo_serio` - Reach rank Sub-Chefe ✅
- `carteira_pesada` - Reach 1,000,000,000 money ✅
- `pvp_casual` - Win first PvP combat ✅
- `negociante_de_rua` - Sell 10,000+ blocks ✅
- `engenharia_manual` - Use anvil 25 times ✅
- `capataz_da_mina` - Play 12 hours total ✅
- `construtor_iniciado` - Create first plot ✅
- `forca_bruta` - Use Efficiency V pickaxe ✅
- `tatica_de_grupo` - Give plot permit to friend ✅

#### **Gold Achievements**
- `mao_de_diamante` - Collect 1,000 diamonds ✅
- `rei_da_mina` - Reach last rank without prestige ✅
- `pvp_dominante` - Win 50 PvP combats ✅
- `milionario_a_vista` - Reach 450,000,000,000 money ✅
- `veterano_ativo` - Play 50 hours total ✅
- `pisaste_cada_pedra` - Visit all mines up to Prestige 1 ✅
- `top_10_mineracao` - Enter top 10 weekly blocks ✅
- `comerciante_solido` - Make 250 sales in /shop ✅
- `minador_imparavel` - Mine 250,000 blocks ✅
- `negocio_fechado` - Sell 1,000,000+ worth in /loja ✅

#### **Platinum Achievements**
- `lenda_da_mina` - Reach Prestige 1 ✅
- `milionario_oficial` - Reach 1,000,000,000,000 money ✅
- `top_dos_tops` - Enter top 10 of any leaderboard ✅
- `senhor_da_picareta` - Break 5 enchanted pickaxes ✅
- `pvp_sanguinario` - Kill 150 players in PvP ✅
- `tempo_e_ouro` - Play 150 hours total ✅
- `cacador_de_eventos` - Participate in 25 events ✅
- `a_maquina` - Mine 1,000,000 blocks ✅
- `comerciante_lendario` - Sell 10,000,000+ total ✅

#### **Mythic Achievements**
- `deus_da_mina` - Reach Prestige 10 ✅
- `sombra_do_pvp` - Kill 500 players in PvP ✅
- `conquistador_de_sangue` - Dominate 10 weekly PvP events ⚠️
- `nome_gravado_em_pedra` - Stay in top 3 for 7 days ✅
- `tempo_e_poder` - Play 500 hours total ✅
- `inquebravel` - No PvP deaths for 30 days ✅
- `lenda_absoluta` - Complete 100% achievements ⚠️

### **Integration Points for Manual Tracking**

Some achievements require manual integration with specific systems:

#### **Shop/Selling System Integration**
```java
// When player sells items in your shop system:
AchievementShopManager.getInstance().onPlayerSell(player, itemCount, saleValue);
```

#### **Mine Visit Tracking**
```java
// When player enters a mine area:
AchievementSpecialManager.getInstance().onMineVisit(player, mineName);
```

#### **Tool Usage Tracking**
```java
// When player uses anvil:
AchievementSpecialManager.getInstance().onAnvilUse(player);

// When tool breaks:
AchievementSpecialManager.getInstance().onToolBreak(player, brokenTool);

// When using Efficiency V pickaxe:
AchievementSpecialManager.getInstance().onEfficiencyVUse(player);
```

#### **Plot System Integration**
```java
// When player creates plot:
AchievementSpecialManager.getInstance().onPlotCreate(player);

// When player gives plot permission:
AchievementSpecialManager.getInstance().onPlotPermitGiven(player);
```

#### **Event System Integration**
```java
// When player participates in official event:
AchievementSpecialManager.getInstance().onEventParticipation(player);
```

#### **Leaderboard Integration**
```java
// Daily check if player is in top 10:
AchievementSpecialManager.getInstance().onLeaderboardCheck(player, isInTop10);
```

### **Automatic Tracking**

The following achievements are tracked automatically:
- All block mining achievements
- All PvP kill achievements
- All playtime achievements
- All money achievements (checked every 5 minutes)
- All rank/prestige achievements
- Clan joining achievements
- Crafting achievements

## Next Steps

1. **Manual Integration** - Add the integration points above to your existing systems
2. **Testing** - Test with actual Velocity integration
3. **Fine-tuning** - Adjust thresholds and tracking as needed

## Files Modified/Created

### Created:
- `src/main/java/me/miguel19877/dev/utils/Achievement.java`
- `src/main/java/me/miguel19877/dev/utils/AchievementPlayerData.java`
- `src/main/java/me/miguel19877/dev/utils/AchievementHelper.java`
- `src/main/java/me/miguel19877/dev/managers/AchievementManager.java`
- `src/main/java/me/miguel19877/dev/managers/PlaytimeManager.java`
- `src/main/java/me/miguel19877/dev/managers/AchievementShopManager.java`
- `src/main/java/me/miguel19877/dev/managers/AchievementSpecialManager.java`
- `src/main/java/me/miguel19877/dev/listeners/AchievementMessageListener.java`
- `src/main/java/me/miguel19877/dev/listeners/AchievementTrackingListener.java`
- `src/main/java/me/miguel19877/dev/listeners/AchievementRankListener.java`
- `src/main/java/me/miguel19877/dev/listeners/AchievementClanListener.java`
- `src/main/java/me/miguel19877/dev/commands/Achievements.java`

### Modified:
- `src/main/java/me/miguel19877/dev/Rankup.java` - Added channel registration, command, and manager initialization
- `src/main/java/me/miguel19877/dev/listeners/JoinListener.java` - Added achievement tracking initialization
- `src/main/java/me/miguel19877/dev/listeners/QuitListener.java` - Added achievement cleanup
- `src/main/java/me/miguel19877/dev/commands/Rankup.java` - Added rank achievement triggers
- `src/main/java/me/miguel19877/dev/commands/PrestigeCommand.java` - Added prestige achievement triggers
- `src/main/java/me/miguel19877/dev/commands/ClanCommand.java` - Added clan achievement triggers
- `src/main/java/me/miguel19877/dev/utils/PlayerDeltaManager.java` - Added achievement method
- `src/main/resources/plugin.yml` - Added achievements command
- `src/main/resources/messages_en.yml` - Added achievement messages
- `src/main/resources/messages_pt.yml` - Added achievement messages

The system is now ready for achievement tracking implementation!
